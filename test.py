if __name__ == "__main__":


    from threading import Thread, Event
    import pandas as pd
    import pandas_ta as ta
    import yfinance as yf

    from lightweight_charts import Chart
    from ibapi.wrapper import EWrapper
    from ibapi.client import EClient
    import time 
    from ibapi.common import *
    from ibapi.contract import Contract
    from typing import Any
    import decimal



    class ibapp(EClient, EWrapper):
        def __init__(self):
            EClient.__init__(self, self)
            self.done = Event()  # use threading.Event to signal between threads
            self.connection_ready = Event()  # to signal the connection has been established
            self.bars = [] 

        # override Ewrapper.error
        def error(
            self, reqId: TickerId, errorCode: int, errorString: str, contract: Any = None
        ):
            print("Error: ", reqId, " ", errorCode, " ", errorString)
            if errorCode == 502:  # not connected
                # set self.done (a threading.Event) to True
                self.done.set()

        def historicalData(self, reqId, bar):
            clean_date = bar.date.split(" ")[0]
            self.bars.append([clean_date, bar.open, bar.high, bar.low, bar.close, bar.volume])

            print(bar)

    chart =  Chart()

    app =  ibapp()

    app.connect("127.0.0.1", 7497, clientId=5) 

    time.sleep(1)



    api_thread = Thread(target=app.run, args=(), daemon=True)
    api_thread.start()

    time.sleep(1)

    con = Contract()
    con.symbol = "TSLA"
    con.secType = "STK"
    con.exchange = "SMART"
    con.currency = "USD"
    con.primaryExchange = "NASDAQ"

    app.reqHistoricalData(0, con, "", "1 D", "1 min", "TRADES", 0, 1, False, [])   

    print(time.time())
    time.sleep(2)

    print(time.time())
    df = pd.DataFrame(app.bars, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])
    df['Date'] = pd.to_datetime(df['Date'], format="%Y%m%d")  # Explicit format


    df = df.map(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)


    chart.set(df)

    chart.show()