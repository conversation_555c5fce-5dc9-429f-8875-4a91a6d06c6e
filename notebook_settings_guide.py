"""
Jupyter Notebook Built-in Settings and Magic Commands for Callback Print Capture
This shows you the native notebook features you can use
"""

# === JUPYTER NOTEBOOK BUILT-IN SETTINGS ===

# 1. IPython Configuration Settings
# You can set these in your notebook or IPython profile

from IPython.core.interactiveshell import InteractiveShell
from IPython.display import display, HTML
import sys
import io

def setup_notebook_output_settings():
    """Configure notebook output settings"""
    
    # Get the current IPython instance
    shell = InteractiveShell.instance()
    
    # Configure output settings
    shell.ast_node_interactivity = "all"  # Show all expressions, not just last one
    
    # Set maximum output length (useful for long callback outputs)
    shell.cache_size = 1000  # Keep more outputs in memory
    
    print("✅ Notebook output settings configured")
    print("   - ast_node_interactivity: all")
    print("   - cache_size: 1000")


# 2. Magic Commands for Output Capture
def show_magic_commands():
    """Show useful magic commands for callback capture"""
    
    magic_commands = """
    === USEFUL JUPYTER MAGIC COMMANDS ===
    
    # Capture cell output to variable
    %%capture output
    your_callback_code_here()
    # Then access with: output.stdout, output.stderr
    
    # Time execution and show output
    %%time
    your_callback_code_here()
    
    # Run cell in background (useful for callbacks)
    %%script python --bg --out output --err error
    your_callback_code_here()
    
    # Redirect stdout to file
    %%writefile -a callback_output.txt
    your_callback_code_here()
    
    # Load and run external script
    %run your_callback_script.py
    
    # Auto-reload modules (useful for development)
    %load_ext autoreload
    %autoreload 2
    """
    
    print(magic_commands)


# 3. Built-in Output Widgets
def setup_output_widgets():
    """Setup IPython output widgets for callback monitoring"""
    try:
        import ipywidgets as widgets
        from IPython.display import display
        
        # Create output widget
        output_widget = widgets.Output()
        
        # Display the widget
        display(output_widget)
        
        # Function to capture prints to widget
        def capture_to_widget(func, *args, **kwargs):
            with output_widget:
                return func(*args, **kwargs)
        
        print("✅ Output widget created")
        print("Use: capture_to_widget(your_function, args)")
        
        return output_widget, capture_to_widget
        
    except ImportError:
        print("❌ ipywidgets not available")
        return None, None


# 4. Built-in Logging Configuration
def setup_notebook_logging():
    """Setup logging to capture callback output"""
    import logging
    import sys
    
    # Create logger
    logger = logging.getLogger('callback_logger')
    logger.setLevel(logging.DEBUG)
    
    # Create handler that writes to notebook
    class NotebookHandler(logging.Handler):
        def emit(self, record):
            print(f"[{record.levelname}] {record.getMessage()}")
    
    handler = NotebookHandler()
    logger.addHandler(handler)
    
    # Redirect print to logger (optional)
    class LoggerWriter:
        def __init__(self, logger, level):
            self.logger = logger
            self.level = level
            
        def write(self, message):
            if message.strip():
                self.logger.log(self.level, message.strip())
                
        def flush(self):
            pass
    
    print("✅ Notebook logging setup complete")
    print("Use: logger.info('your message') or redirect sys.stdout")
    
    return logger


# 5. Notebook Cell Configuration
NOTEBOOK_CELL_CONFIGS = """
=== NOTEBOOK CELL CONFIGURATIONS ===

# At the top of your notebook, add these configurations:

# 1. Auto-reload modules
%load_ext autoreload
%autoreload 2

# 2. Rich output formatting
from rich import print
from rich.traceback import install
install()

# 3. Configure IPython display
from IPython.core.interactiveshell import InteractiveShell
InteractiveShell.ast_node_interactivity = "all"

# 4. Set up output capture
from IPython.utils.capture import capture_output

# 5. Configure matplotlib for inline plots (if needed)
%matplotlib inline

# 6. Set pandas display options (if using pandas)
import pandas as pd
pd.set_option('display.max_rows', 100)
pd.set_option('display.max_columns', 20)
"""


# 6. Built-in Capture Methods
def demonstrate_builtin_capture():
    """Demonstrate built-in Jupyter capture methods"""
    
    print("=== BUILT-IN CAPTURE METHODS ===\n")
    
    # Method 1: Using %%capture magic
    capture_example = '''
# In a notebook cell:
%%capture captured_output
print("This will be captured")
your_callback_function()

# In next cell:
print("Captured stdout:", captured_output.stdout)
print("Captured stderr:", captured_output.stderr)
'''
    
    # Method 2: Using IPython's capture_output
    from IPython.utils.capture import capture_output
    
    def demo_function():
        print("This is a demo callback")
        return "result"
    
    with capture_output() as captured:
        result = demo_function()
    
    print("Method 1 - %%capture magic:")
    print(capture_example)
    
    print("\nMethod 2 - IPython capture_output:")
    print(f"Captured stdout: {captured.stdout}")
    print(f"Function result: {result}")


# 7. Notebook Configuration File Settings
NOTEBOOK_CONFIG_SETTINGS = """
=== JUPYTER NOTEBOOK CONFIGURATION ===

# Create/edit: ~/.jupyter/jupyter_notebook_config.py

# Increase output buffer size
c.NotebookApp.iopub_data_rate_limit = 10000000  # 10MB
c.NotebookApp.iopub_msg_rate_limit = 3000       # 3000 msgs/sec

# Configure kernel settings
c.KernelManager.shutdown_wait_time = 30.0

# For JupyterLab, edit: ~/.jupyter/jupyter_lab_config.py
c.ServerApp.iopub_data_rate_limit = 10000000
c.ServerApp.iopub_msg_rate_limit = 3000

# Or set environment variables:
# JUPYTER_IOPUB_DATA_RATE_LIMIT=10000000
# JUPYTER_IOPUB_MSG_RATE_LIMIT=3000
"""


# 8. Runtime Configuration
def configure_notebook_runtime():
    """Configure notebook runtime for better callback handling"""
    
    # Increase recursion limit if needed
    import sys
    sys.setrecursionlimit(3000)
    
    # Configure threading for callbacks
    import threading
    threading.stack_size(32768)  # 32KB stack size
    
    # Set up signal handling for interrupts
    import signal
    
    def signal_handler(signum, frame):
        print(f"\n⚠️ Signal {signum} received - cleaning up...")
        # Add cleanup code here
    
    signal.signal(signal.SIGINT, signal_handler)
    
    print("✅ Runtime configuration complete")
    print("   - Recursion limit: 3000")
    print("   - Thread stack size: 32KB")
    print("   - Signal handlers: configured")


# 9. Complete Setup Function
def setup_notebook_for_callbacks():
    """Complete setup for callback print capture"""
    
    print("🔧 Setting up notebook for callback capture...\n")
    
    # 1. Configure output settings
    setup_notebook_output_settings()
    
    # 2. Setup logging
    logger = setup_notebook_logging()
    
    # 3. Setup output widgets
    output_widget, capture_func = setup_output_widgets()
    
    # 4. Configure runtime
    configure_notebook_runtime()
    
    # 5. Show magic commands
    show_magic_commands()
    
    print("\n✅ Notebook setup complete!")
    print("📋 Configuration summary:")
    print(NOTEBOOK_CELL_CONFIGS)
    print(NOTEBOOK_CONFIG_SETTINGS)
    
    return {
        'logger': logger,
        'output_widget': output_widget,
        'capture_func': capture_func
    }


# 10. Quick Setup for Your Trading Code
def quick_trading_setup():
    """Quick setup specifically for your trading callbacks"""
    
    # Auto-reload for development
    try:
        get_ipython().run_line_magic('load_ext', 'autoreload')
        get_ipython().run_line_magic('autoreload', '2')
        print("✅ Auto-reload enabled")
    except:
        print("⚠️ Auto-reload not available (not in IPython)")
    
    # Rich formatting
    try:
        from rich import print as rich_print
        from rich.traceback import install
        install()
        print("✅ Rich formatting enabled")
    except ImportError:
        print("⚠️ Rich not available")
    
    # Configure display
    try:
        from IPython.core.interactiveshell import InteractiveShell
        InteractiveShell.ast_node_interactivity = "all"
        print("✅ Display all expressions enabled")
    except:
        pass
    
    print("\n🚀 Quick setup complete! Your notebook is ready for callback capture.")


if __name__ == "__main__":
    print("Jupyter Notebook Settings Guide")
    print("=" * 50)
    demonstrate_builtin_capture()
    print("\n" + "=" * 50)
    print("Run setup_notebook_for_callbacks() to configure everything")
