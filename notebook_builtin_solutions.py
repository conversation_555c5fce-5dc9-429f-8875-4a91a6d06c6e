"""
BUILT-IN JUPYTER NOTEBOOK SOLUTIONS FOR <PERSON><PERSON><PERSON><PERSON><PERSON> PRINTS
Copy these directly into your notebook cells - no external files needed!
"""

# === SOLUTION 1: BUILT-IN %%capture MAGIC ===
CAPTURE_MAGIC_EXAMPLE = '''
# Cell 1: Use %%capture magic to capture all output
%%capture callback_output
req = tt.ib.reqContractDetails(9, contract)
req.done.delay_wait()

# Cell 2: Display captured output
print("=== CAPTURED CALLBACK OUTPUT ===")
print(callback_output.stdout)
if callback_output.stderr:
    print("ERRORS:")
    print(callback_output.stderr)
'''

# === SOLUTION 2: BUILT-IN capture_output CONTEXT MANAGER ===
def builtin_capture_demo():
    """Demo using IPython's built-in capture_output"""
    from IPython.utils.capture import capture_output
    
    print("Using IPython's built-in capture_output:")
    
    # Your callback code here
    def simulate_callback():
        print("This is callback output")
        print("More callback data...")
        return "callback_result"
    
    # Capture the output
    with capture_output() as captured:
        result = simulate_callback()
    
    # Display captured output
    print("\n📋 Captured Output:")
    print(captured.stdout)
    print(f"📊 Function Result: {result}")


# === SOLUTION 3: NOTEBOOK CONFIGURATION SETTINGS ===
NOTEBOOK_CONFIG_CELL = '''
# === COPY THIS INTO YOUR FIRST NOTEBOOK CELL ===

# 1. Auto-reload modules (so changes take effect immediately)
%load_ext autoreload
%autoreload 2

# 2. Show all expressions, not just the last one
from IPython.core.interactiveshell import InteractiveShell
InteractiveShell.ast_node_interactivity = "all"

# 3. Rich output formatting (if available)
try:
    from rich import print
    from rich.traceback import install
    install()
    print("✅ Rich formatting enabled")
except ImportError:
    print("⚠️ Rich not available, using standard print")

# 4. Configure pandas display (if using pandas)
try:
    import pandas as pd
    pd.set_option('display.max_rows', 100)
    pd.set_option('display.max_columns', 20)
except ImportError:
    pass

print("🔧 Notebook configuration complete!")
'''

# === SOLUTION 4: BUILT-IN OUTPUT WIDGETS ===
def setup_output_widget():
    """Setup IPython output widget for callback monitoring"""
    try:
        import ipywidgets as widgets
        from IPython.display import display
        
        # Create output widget
        output = widgets.Output()
        display(output)
        
        print("✅ Output widget created above")
        print("Use the returned widget to capture callback output")
        
        return output
    except ImportError:
        print("❌ ipywidgets not installed")
        print("Install with: pip install ipywidgets")
        return None

# === SOLUTION 5: MAGIC COMMANDS FOR YOUR USE CASE ===
MAGIC_COMMANDS_EXAMPLES = '''
=== USEFUL MAGIC COMMANDS FOR CALLBACK CAPTURE ===

# 1. Capture all output from a cell
%%capture my_output
req = tt.ib.reqContractDetails(9, contract)
req.done.delay_wait()
# Access with: my_output.stdout, my_output.stderr

# 2. Time execution and show all output
%%time
req = tt.ib.reqContractDetails(9, contract)
req.done.delay_wait()

# 3. Write output to file
%%writefile -a trading_log.txt
req = tt.ib.reqContractDetails(9, contract)
req.done.delay_wait()

# 4. Run in background and capture output
%%script python --bg --out bg_output --err bg_error
import time
time.sleep(5)
print("Background task completed")

# 5. Load external script
%run your_trading_script.py

# 6. Execute system command
!echo "System command output"
'''

# === SOLUTION 6: BUILT-IN LOGGING SETUP ===
def setup_builtin_logging():
    """Setup Python's built-in logging for callback capture"""
    import logging
    import sys
    from datetime import datetime
    
    # Create custom handler that prints to notebook
    class NotebookHandler(logging.Handler):
        def emit(self, record):
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"[{timestamp}] {record.levelname}: {record.getMessage()}")
    
    # Setup logger
    logger = logging.getLogger('trading_callbacks')
    logger.setLevel(logging.INFO)
    
    # Add notebook handler
    handler = NotebookHandler()
    logger.addHandler(handler)
    
    print("✅ Built-in logging setup complete")
    print("Usage: logger.info('Your callback message')")
    
    return logger

# === SOLUTION 7: ENVIRONMENT VARIABLES FOR JUPYTER ===
JUPYTER_ENV_SETTINGS = '''
=== JUPYTER ENVIRONMENT SETTINGS ===

# Set these environment variables before starting Jupyter:

# Increase output buffer limits
export JUPYTER_IOPUB_DATA_RATE_LIMIT=10000000
export JUPYTER_IOPUB_MSG_RATE_LIMIT=3000

# Or add to your shell profile (.bashrc, .zshrc, etc.):
echo 'export JUPYTER_IOPUB_DATA_RATE_LIMIT=10000000' >> ~/.bashrc
echo 'export JUPYTER_IOPUB_MSG_RATE_LIMIT=3000' >> ~/.bashrc

# For Windows Command Prompt:
set JUPYTER_IOPUB_DATA_RATE_LIMIT=10000000
set JUPYTER_IOPUB_MSG_RATE_LIMIT=3000

# For Windows PowerShell:
$env:JUPYTER_IOPUB_DATA_RATE_LIMIT="10000000"
$env:JUPYTER_IOPUB_MSG_RATE_LIMIT="3000"
'''

# === COMPLETE BUILT-IN SOLUTION ===
def complete_builtin_setup():
    """Complete setup using only built-in Jupyter features"""
    
    print("🔧 Setting up callback capture with built-in Jupyter features...\n")
    
    # 1. Configure IPython
    try:
        from IPython.core.interactiveshell import InteractiveShell
        shell = InteractiveShell.instance()
        shell.ast_node_interactivity = "all"
        print("✅ IPython configured to show all expressions")
    except:
        print("⚠️ Not in IPython environment")
    
    # 2. Setup logging
    logger = setup_builtin_logging()
    
    # 3. Setup output widget
    output_widget = setup_output_widget()
    
    # 4. Show configuration
    print("\n📋 Built-in Solutions Available:")
    print("1. %%capture magic command")
    print("2. IPython capture_output context manager")
    print("3. Output widgets (if ipywidgets installed)")
    print("4. Built-in logging")
    print("5. Magic commands (%%time, %%writefile, etc.)")
    
    print("\n🚀 Setup complete! Use the methods shown above.")
    
    return {
        'logger': logger,
        'output_widget': output_widget
    }

# === READY-TO-USE EXAMPLES ===
READY_TO_USE_EXAMPLES = '''
=== READY-TO-USE EXAMPLES FOR YOUR TRADING CODE ===

# Example 1: Simple capture with %%capture
%%capture trading_output
from ibapi.contract import Contract
con = Contract()
con.symbol = "AAPL"
con.secType = "STK"
con.exchange = "SMART"
con.currency = "USD"

req = tt.ib.reqContractDetails(9, con)
req.done.delay_wait()

# Then in next cell:
print("Captured output:")
print(trading_output.stdout)

# Example 2: Using capture_output context manager
from IPython.utils.capture import capture_output

with capture_output() as captured:
    req = tt.ib.reqContractDetails(9, contract)
    req.done.delay_wait()

print("Callback output:", captured.stdout)

# Example 3: Using output widget
output_widget = setup_output_widget()
with output_widget:
    req = tt.ib.reqContractDetails(9, contract)
    req.done.delay_wait()

# Example 4: Using logging
logger = setup_builtin_logging()
# Modify your callback to use: logger.info("callback message")
'''

if __name__ == "__main__":
    print("Built-in Jupyter Notebook Solutions")
    print("=" * 50)
    print(NOTEBOOK_CONFIG_CELL)
    print("\n" + "=" * 50)
    print(MAGIC_COMMANDS_EXAMPLES)
    print("\n" + "=" * 50)
    print(JUPYTER_ENV_SETTINGS)
    print("\n" + "=" * 50)
    print(READY_TO_USE_EXAMPLES)
