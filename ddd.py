import time
import pandas as pd
from lightweight_charts import Chart

import os

# Print current working directory
print("Before:", os.getcwd())

# Set the working directory to your desired folder path
os.chdir(r'C:\TWS API\TradeTool')

# Verify that the working directory has been changed
print("After:", os.getcwd())

def get_bar_data(symbol, timeframe):
    if symbol not in ('AAPL', 'GOOGL', 'TSLA'):
        print(f'No data for "{symbol}"')
        return pd.DataFrame()
    print(f'bar_data/{symbol}_{timeframe}.csv')
    return pd.read_csv(f'bar_data/{symbol}_{timeframe}.csv')


def on_search(chart, *_):  # Called when the user searches.
    searched_string = chart.topbar['symbol'].value
    new_data = get_bar_data(searched_string, chart.topbar['timeframe'].value)
    if new_data.empty:
        return
    chart.topbar['symbol'].set(searched_string)
    chart.set(new_data)


def on_timeframe_selection(chart):  # Called when the user changes the timeframe.
    new_data = get_bar_data(chart.topbar['symbol'].value, chart.topbar['timeframe'].value)
    if new_data.empty:
        return
    chart.set(new_data, True)


def on_horizontal_line_move(chart, line):
    print(f'Horizontal line moved to: {line.price}')


if __name__ == '__main__':
    chart = Chart(toolbox=True)
    chart.legend(True)

    chart.events.search += on_search

    chart.topbar.textbox('symbol', 'TSLA',func=on_search)
    chart.topbar.switcher('timeframe', ('1min', '5min', '30min'), default='5min',
                          func=on_timeframe_selection)
    chart.events.search += on_search

    df = get_bar_data('TSLA', '5min')
    chart.set(df)

    chart.horizontal_line(200, func=on_horizontal_line_move)

    chart.show(block=True)

