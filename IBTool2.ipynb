{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2fb37856", "metadata": {}, "outputs": [], "source": ["from threading import Thread, Event, current_thread\n", "import time\n", "from datetime import datetime\n", "\n", "import inspect\n", "\n", "from rich import print\n", "from rich.traceback import install\n", "\n", "install()\n", "\n", "class WARNING_CANCEL_UPDATEACCOUNT():\n", "    MESSAGES = {\n", "        2100: \"从 TWS请 求 了 新 账 户 数 据 。 API客 户 已 从 账 户 数 据 中 取 消 订阅 。\"\n", "    }\n", "\n", "\n", "class WARNING:\n", "    MESSAGES = {\n", "        399,  \"Order Message: BUY 1 TSLA NASDAQ.NMS Warning: Your order will not be placed at the exchange until xx time.\"\n", "    }    \n", "\n", "class INFO_TWS_AVILABLE():\n", "    FAIL_MESSAGES = {\n", "        1100: \"Connectivity between IBKR and Trader Workstation has been lost., contract\",\n", "    }\n", "\n", "    SUCCESS_MESSAGES = {\n", "        1102:\"Connectivity between IBKR and Trader Workstation has been restored - data maintained. All data farms are connected: usfarm.nj; jfarm; cafarm; cashfarm; usfarm; euhmds; fundfarm; ushmds; secdefil., contract\"\n", "    }\n", "\n", "class ERROR_CONNECTION():\n", "    MESSAGES = {\n", "        502:\"不 能 连 接 TWS。 通 过 Configure>API( 配 置 >API) 菜 单 指 令 确认 API已 在 TWS中 启 用\",\n", "        503:\"你 的 TWS版 本 已 过 期 , 必 须 更 新\",\n", "        504:\"没 有 连 接 。\",\n", "        1100: \"Connectivity between IBKR and Trader Workstation has been lost., contract\"\n", "    }\n", "\n", "\n", "class CLIENT_REQUEST_ERRORS():\n", "    CONNECTION_UNAVAILABLE = {\"errorCode\":99910,\"errorString\":\"Connection unavailable\"}\n", "    TIME_OUT = {\"errorCode\":99911,\"errorString\":\"Request timed out\"}\n", "\n", "\n", "class MESSAGE_DONE():\n", "    MESSAGES = {\n", "        202: \"Order Canceled - reason\"\n", "    }\n", "\n", "\n", "class TickerID_RULES():\n", "    SYSTEM = -1\n", "    REQUEST_ID_MIN = 0\n", "    REQUEST_ID_MAX = 20\n", "    \n", "\n", "class DelayEvent(Event):\n", "    def __init__(self, timeout: float = 0.0,delay: float = 0.0, callbackWhenFinished = None):\n", "        super().__init__()\n", "        self.delay = delay\n", "        self.timeout = timeout\n", "        self.callbackWhenFinished = callbackWhenFinished\n", "\n", "\n", "    def wait(self):\n", "        result = False\n", "\n", "        if self.timeout > 0.0:\n", "            result = super().wait(self.timeout)\n", "          \n", "        else:\n", "            result = super().wait()\n", "    \n", "        \n", "        time.sleep(self.delay)\n", "        if self.callback<PERSON>henFinished is not None:\n", "            self.callback<PERSON><PERSON><PERSON>inished(self, result)\n", "\n", "        return result\n", "    \n", "\n", "class REQUEST_TYPES():\n", "    PLACE_ORDER = 1\n", "    UPDATE_ORDER = 2\n", "    CANCEL_ORDER = 3\n", "    GET_OPEN_ORDERS = 4\n", "    GET_EXECUTIONS = 5\n", "    GET_COMPLETED_ORDERS = 6\n", "    GET_POSITIONS = 7\n", "    CANCEL_POSITIONS = 8\n", "    GET_IDS = 9\n", "    UPDATE_ACCOUNT_SUMMARY = 10\n", "    CANCEL_ACCOUNT_SUMMARY = 11\n", "    UPDATE_ACCOUNT = 12\n", "    CANCEL_UPDATE_ACCOUNT = 13\n", "    GET_HISTORICAL_DATA = 14\n", "    GET_REAL_TIME_BARS = 15\n", "    CANCEL_REAL_TIME_BARS = 16\n", "    GET_MARKET_DATA = 17\n", "    CANCEL_MARKET_DATA = 18\n", "    GET_HISTORICAL_TICKS = 19\n", "    CANCEL_HISTORICAL_TICKS = 20\n", "    GET_TICK_BY_TICK_DATA = 21\n", "    CANCEL_TICK_BY_TICK_DATA = 22\n", "    GET_FUNDAMENTAL_DATA = 23\n", "    CANCEL_FUNDAMENTAL_DATA = 24\n", "    GET_MARKET_DEPTH = 25\n", "    CANCEL_MARKET_DEPTH = 26\n", "    GET_MARKET_DEPTH_L2 = 27\n", "    CANCEL_MARKET_DEPTH_L2 = 28\n", "    GET_CURRENT_TIME = 29\n", "    CONNECT = 30\n", "    REQUEST_ERROR = 99999\n", "class REQUEST_TYPES_ALLOW_DUPLICATE():\n", "    LIST = [REQUEST_TYPES.GET_HISTORICAL_DATA]\n", "   \n", "\n", "\n", "class Request():\n", "    def __init__(self, type : REQUEST_TYPES, reqId: int = -1, keepAlive: bool = False, timeout: float = 0.0, delayWhenFinished: float = 0.0, callbackWhenFinished = None, noResponse: bool = False):\n", "        self.type = type\n", "        self.reqId = reqId\n", "        self.done = DelayEvent(timeout, delayWhenFinished , callbackWhenFinished = self.handle_when_done)\n", "        self.keepAlive = keepAlive\n", "        self.response = Response()\n", "        self.callbackWhenFinished = callbackWhenFinished\n", "        self.isTimeout = False\n", "        self.notRequireResponse =  noResponse\n", "   \n", "\n", "    def handle_when_done(self, event: DelayEvent, timeout: bool):\n", "        self.isTimeout = timeout\n", "        if timeout == True:\n", "            if  self.notRequireResponse == False:\n", "                self.response.errors.append(CLIENT_REQUEST_ERRORS.TIME_OUT)\n", "            \n", "\n", "        self.callback<PERSON><PERSON><PERSON><PERSON><PERSON>(self)\n", "\n", "\n", "class Response():\n", "\n", "    def __init__(self):\n", "        self.contents = []\n", "        self.errors = []\n", "        self.warnings = []\n", "        self.infos = []\n", "\n", "\n", "class Logger():\n", "    def __init__(self, config):\n", "        self.config = config\n", "\n", "    def log(self, message):\n", "        print(message)\n", "\n", "    def log(message):\n", "        stack = inspect.stack()\n", "        if stack[1].function != \"historicalData1\":\n", "            print(f\"caller:{stack[1].function} {datetime.now():%Y-%m-%d %H:%M:%S} {message}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "363efc68", "metadata": {}, "outputs": [], "source": ["from threading import Thread, Event\n", "from ibapi.order_cancel import OrderCancel\n", "from ibapi.wrapper import EWrapper\n", "from ibapi.client import EClient\n", "from ibapi.common import *\n", "from ibapi.contract import Contract\n", "from ibapi.order import Order\n", "from ibapi.order_state import OrderState\n", "from ibapi.execution import ExecutionFilter\n", "from ibapi.commission_report import *\n", "\n", "from functools import wraps\n", "\n", "from pandas import DataFrame as df\n", "from ibapi.tag_value import TagValue \n", "\n", "from typing import Any\n", "\n", "\n", "from collections import defaultdict\n", "from decimal import Decimal\n", "\n", "\n", "\n", "\n", "from typing import List\n", "ListOfTagValue = List[TagValue]\n", "\n", "class IBTool(EClient, EWrapper):\n", "    def __init__(self, config):\n", "        EClient.__init__(self, self)\n", "        self.config = config\n", "        self.nextOrderId = 0\n", "        self.accountSummaryData = {}\n", "        self.accountData = {}\n", "        self.protfolioData = {}\n", "        self.accountUpdateTime = \"\"\n", "        self.openOrdersData = {}\n", "        self.openStatusData = {}\n", "        self.execDetailsData = {}\n", "        self.completedOrdersData = {}\n", "        self.currentTime = 0\n", "\n", "        self.requests = []\n", "    \n", "        self.TWS_connection = False\n", "        self.TWS_aviliable = True\n", "        self.thread_run = None\n", "        self.thread_connection = None\n", "\n", "    def register_requests(self, type: REQUEST_TYPES, reqId: int = -1, keepAlive: bool = False, timeout: float = 0.0, delayWhenFinished: float = 0.0 , noResponse: bool = False):\n", "        if type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:\n", "            if self.find_unduplicate_running_request():\n", "                raise Exception(f\"Request { self.get_request_type_name(type) }, no more than one unduplicated request allowed.\")\n", "\n", "        # if type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:\n", "        #     if self.find_request(type, reqId) is not None:\n", "        #         raise Exception(f\"Request { self.get_request_type_name(type) } already exists and is not allowed to be duplicated.\")\n", "        \n", "\n", "\n", "\n", "        req = Request(type, reqId = reqId, keepAlive = keepAlive, timeout = timeout, delayWhenFinished =  delayWhenFinished , callbackWhenFinished = self.unregister_requests_without_KeepAlive, noResponse = noResponse)\n", "        req.response = Response()\n", "        self.requests.append(req)\n", "\n", "        return req\n", "\n", "\n", "    def unregister_requests(self, type: REQUEST_TYPES, reqId: int = -1):\n", "        req = self.find_request(type, reqId)\n", "        if req is not None:\n", "            self.requests.remove(req)\n", "\n", "    def unregister_requests_without_KeepAlive(self, req: Request):\n", "        type = req.type\n", "        reqId = req.reqId\n", "        print(f\"unregister_requests_without_KeepAlive. \")\n", "        for req in self.requests:\n", "            if req.type == type and (reqId == -1 or req.reqId == reqId):\n", "                print(f\"unregister_requests_without_KeepAlive. reqId: {req.reqId}, type: {self.get_request_type_name(req.type)}  type2: {self.get_request_type_name(type)} reqId2: {reqId}\")\n", "                if not req.keepAlive:\n", "                    self.requests.remove(req)\n", "\n", "\n", "    def find_request(self, type: REQUEST_TYPES, reqId: int = -1):\n", "        for req in self.requests:\n", "            if req.type == type and (reqId == -1 or req.reqId == reqId):\n", "                return req\n", "        return None\n", "    \n", "    def find_request_by_reqId(self, reqId: int):\n", "        for req in self.requests:\n", "            if req.reqId == reqId:\n", "                return req\n", "        return None\n", "    \n", "    def find_request_by_type_list(self, type_list: list[REQUEST_TYPES], reqId: int = -1):\n", "        for req in self.requests:\n", "            if req.type in type_list and (reqId == -1 or req.reqId == reqId):\n", "                return req\n", "        return None\n", "    \n", "    def find_unduplicate_running_request(self):\n", "        for req in self.requests:\n", "            if not req.keepAlive and req.type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:\n", "                return req\n", "        \n", "        return None\n", "    \n", "    def get_request_type_name(self, value):\n", "        for name, val in REQUEST_TYPES.__dict__.items():\n", "            if val == value:\n", "                return name\n", "        return None\n", "\n", "\n", "    @staticmethod\n", "    def RequestWarper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", "\n", "            retry_count = 0\n", "            max_retry = 300\n", "            while retry_count < max_retry:\n", "                if not (self.TWS_connection  and super().isConnected() and self.TWS_aviliable) :\n", "                    \n", "                    if not (self.TWS_connection  and super().isConnected()):\n", "                        if self.thread_connection is  None or not self.thread_connection.is_alive():\n", "                            self.thread_connection = Thread(target=self.reconnect, daemon=True)\n", "                            self.thread_connection.start()\n", "\n", "\n", "                    if retry_count % 10 == 0:\n", "                        Logger.log(f\"Request {func.__name__} is waiting for the connection OK. isConnected:{super().isConnected()} TWS_connection: {self.TWS_connection}, TWS_aviliable: {self.TWS_aviliable}\")\n", "\n", "                    retry_count += 1\n", "                    time.sleep(1)\n", "\n", "                    if retry_count == max_retry:\n", "                        req = Request(REQUEST_TYPES.REQUEST_ERROR)\n", "                        req.response = Response()   \n", "                        req.response.errors.append(CLIENT_REQUEST_ERRORS.CONNECTION_UNAVAILABLE)\n", "                        return req\n", "                else:\n", "                    break\n", "            \n", "            if self.thread_connection is not None and self.thread_connection.is_alive():\n", "                self.thread_connection.join()\n", "\n", "            Logger.log(f\"RequestWrapper {func.__name__} : {args} {kwargs}\")\n", "            return func(self, *args, **kwargs)\n", "        return wrapper\n", "\n", "    @RequestWarper\n", "    def placeOrder(self, contract: Contract, order: Order):\n", "        req = self.register_requests(REQUEST_TYPES.PLACE_ORDER, self.nextOrderId,timeout=10, delayWhenFinished=0.1)\n", "        super().placeOrder(self.nextOrderId, contract, order)\n", "        self.nextOrderId += 1\n", "        return req\n", "\n", "    def openOrder(self, orderId: int, contract: Contract, order: Order, orderState: OrderState):\n", "        Logger.log(f\"openOrder. orderId: {orderId}, contract: {contract}, order: {order}, orderState: {orderState}\")        \n", "        self.openOrdersData[orderId] = {\"contract\": contract, \"order\": order, \"orderState\": orderState}\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER, REQUEST_TYPES.UPDATE_ORDER,REQUEST_TYPES.GET_OPEN_ORDERS],orderId) \n", "        if req is not None:\n", "            if len(req.response.contents) == 0:\n", "                req.response.contents.append({\"openOrdersData\": self.openOrdersData})\n", "            else:\n", "                req.response.contents[0][\"openOrdersData\"] = self.openOrdersData\n", "\n", "    \n", "    def get<PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        result = {}\n", "        for openOrderItem in self.openOrdersData.values():\n", "            if openOrderItem[\"orderState\"].status != \"PendingCancel\":\n", "                result[openOrderItem[\"order\"].orderId] = openOrderItem\n", "        \n", "        return result\n", "    \n", "    def orderStatus(self, orderId: int, status: str, filled: float, remaining: float, avgFillPrice: float, permId: int, parentId: int, lastFillPrice: float, clientId: int, whyHeld: str, mktCapPrice: float):\n", "        Logger.log(f\"orderStatus. orderId: {orderId}, status: {status}, filled: {filled}, remaining: {remaining}, avgFillPrice: {avgFillPrice}, permId: {permId}, parentId: {parentId}, lastFillPrice: {lastFillPrice}, clientId: {clientId}, whyHeld: {whyHeld}, mktCapPrice: {mktCapPrice}\")\n", "\n", "        self.openStatusData[orderId] = {\"status\": status, \"filled\": filled, \"remaining\": remaining, \"avgFillPrice\": avgFillPrice, \"permId\": permId, \"parentId\": parentId, \"lastFillPrice\": lastFillPrice, \"clientId\": clientId, \"whyHeld\": whyHeld, \"mktCapPrice\": mktCapPrice}\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER,REQUEST_TYPES.UPDATE_ORDER],orderId) \n", "        if req is not None:\n", "            if len(req.response.contents) == 0:\n", "                req.response.contents.append({\"openStatusData\": self.openStatusData})\n", "            else:\n", "                req.response.contents[0][\"openStatusData\"] = self.openStatusData\n", "           \n", "            req.done.set()\n", "\n", "\n", "\n", "    #This will be callback by login successfuly, placeOrder \n", "    def openOrderEnd(self):\n", "        Logger.log(\"openOrderEnd\")\n", "        \n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_OPEN_ORDERS])\n", "        if req is not None:\n", "            req.response.contents.append(self.openOrdersData)\n", "            req.done.set()\n", "\n", "        \n", "\n", "\n", "    #dont has call back, delete and place new order better.\n", "    @RequestWarper\n", "    def updateOrder(self, orderId: int, contract: Contract, order: Order):\n", "        req = self.register_requests(REQUEST_TYPES.UPDATE_ORDER, orderId, timeout=5, delayWhenFinished=0.1)\n", "        super().placeOrder(orderId, contract, order)\n", "        return req\n", "    \n", "    # dont use warpper, because sub-function used\n", "    def replaceOder(self, orderId: int, contract: Contract, order: Order):\n", "        Logger.log(f\"replaceOder. orderId: {orderId}, contract: {contract}, order: {order}\")\n", "\n", "        req =self.cancelOrder(orderId)\n", "        req.done.wait()  \n", "\n", "        if len(req.response.errors) > 0:\n", "            return req\n", "        \n", "        req2 = self.placeOrder(contract, order)\n", "        return req2    \n", "\n", "\n", "    \n", "    @RequestWarper\n", "    def cancelOrder(self, orderId: int ):\n", "        cancelOrder = OrderCancel()\n", "        cancelOrder.manualOrderCancelTime = \"\"\n", "        req =self.register_requests(REQUEST_TYPES.CANCEL_ORDER, orderId, timeout=10, delayWhenFinished=0.1)\n", "        super().cancelOrder(orderId, cancelOrder)\n", "        return req\n", "\n", "    @RequestWarper\n", "    def req<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        req = self.register_requests(REQUEST_TYPES.GET_OPEN_ORDERS, timeout=10, delayWhenFinished=0.1)\n", "        super().reqAllOpenOrders()\n", "        return req\n", "\n", "\n", "        \n", "    def error(self, tickerId: TickerId, errorCode: int, errorString: str , contract: Any = None):\n", "        Logger.log(f\"error. reqId: {tickerId}, errorCode: {errorCode}, errorString: {errorString} contract:{contract}\" )\n", "\n", "        msg = {\"errorCode\": errorCode, \"errorString\": errorString}\n", "\n", "        req = None\n", "        if tickerId != -1:\n", "            req = self.find_request_by_reqId(tickerId) \n", "    \n", "        elif errorCode in WARNING_CANCEL_UPDATEACCOUNT.MESSAGES:\n", "            req = self.find_request(REQUEST_TYPES.UPDATE_ACCOUNT) \n", "            req.keepAlive = False\n", "\n", "        elif errorCode in ERROR_CONNECTION.MESSAGES:\n", "            req = self.find_request(REQUEST_TYPES.CONNECT) \n", "            self.TWS_connection = False        \n", "    \n", "\n", "        if req is not None:\n", "            \n", "            if errorCode in WARNING.MESSAGES:\n", "                req.response.warnings.append(msg)\n", "\n", "            # Cancel in TWS  will not be assign message \n", "            if errorCode in MESSAGE_DONE.MESSAGES:\n", "                req.response.infos.append(msg)\n", "            else:\n", "                req.response.errors.append(msg)\n", "\n", "            req.done.set()\n", "            return\n", "\n", "\n", "        if errorCode in INFO_TWS_AVILABLE.SUCCESS_MESSAGES:\n", "            self.TWS_aviliable = True\n", "        \n", "        elif errorCode in INFO_TWS_AVILABLE.FAIL_MESSAGES:\n", "            self.TWS_aviliable = False\n", "\n", "\n", "\n", "\n", "    @RequestWarper\n", "    def reqExecutions(self, reqId: int, filter: ExecutionFilter):\n", "        req = self.register_requests(REQUEST_TYPES.GET_EXECUTIONS, reqId, timeout=10)\n", "        super().reqExecutions( reqId, filter)\n", "        return req\n", "    \n", "\n", "    def execDetails(self, reqId, contract, execution):\n", "        Logger.log(f\"execDetails. reqId: {reqId}, contract: {contract}, execution: {execution}\")\n", "\n", "        if self.execDetailsData[execution.execId] is None:\n", "            self.execDetailsData[execution.execId] = {}\n", "\n", "        self.execDetailsData[execution.execId][\"contract\"] = contract\n", "        self.execDetailsData[execution.execId][\"execution\"] = execution\n", "   \n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_EXECUTIONS],reqId)\n", "       \n", "        if req is not None:\n", "            req.response.contents.append(self.execDetailsData)\n", "        else:\n", "            raise Exception(\"execDetails without request\")\n", "\n", "    def execDetailsEnd(self, reqId: int):\n", "        Logger.log(f\"execDetailsEnd. reqId: {reqId}\")\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_EXECUTIONS],reqId)\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"execDetailsEnd without request\")\n", "\n", "\n", "    def commissionReport(self, commissionReport: CommissionReport):\n", "        Logger.log(f\"commissionReport. commissionReport: {commissionReport}\")\n", "        \n", "        if self.execDetailsData[commissionReport.execId] is None:\n", "            self.execDetailsData[commissionReport.execId] = {}\n", "        self.execDetailsData[commissionReport.execId][\"commissionReport\"] = commissionReport\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_EXECUTIONS],commissionReport.execId)\n", "        if req is not None:\n", "            req.response.contents.append(self.execDetailsData)\n", "        else:\n", "            raise Exception(\"commissionReport without request\")\n", "\n", "\n", "    def execDetailsEnd(self, reqId: int):\n", "        Logger.log(f\"execDetailsEnd. reqId: {reqId}\")\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_EXECUTIONS],reqId)\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"execDetailsEnd without request\")\n", "        \n", "    \n", "\n", "\n", "    @RequestWarper\n", "    def reqHistoricalData(self, reqId : int, contract: Contract, endDateTime: str, durationStr: str, barSizeSetting: str, whatToShow: str, useRTH: int, formatDate: int, keepUpToDate: bool, chartOptions: ListOfTagValue):\n", "        req = self.register_requests(REQUEST_TYPES.GET_HISTORICAL_DATA, reqId, keepAlive=keepUpToDate)\n", "        super().reqHistoricalData(req.reqId, contract, endDateTime, durationStr, barSizeSetting, whatToShow, useRTH, formatDate, keepUpToDate, chartOptions)\n", "        return req\n", "\n", "\n", "    def historicalData(self, reqId: int, bar: BarData):\n", "        Logger.log(f\"historicalData. reqId: {reqId}, bar: {bar}\")\n", "        req = self.find_request_by_reqId(reqId)\n", "        req.response.contents.append(bar)\n", "\n", "\n", "    def historicalDataUpdate(self, reqId: int, bar: BarData):\n", "        Logger.log(f\"historicalDataUpdate. reqId: {reqId}, bar: {bar}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA],reqId)\n", "        req.response.contents.append(bar)\n", "        \n", "\n", "    def historicalDataEnd(self, reqId: int, start: str, end: str):\n", "        Logger.log(f\"historicalDataEnd. reqId: {reqId}, start: {start}, end: {end}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA],reqId)\n", "        \n", "        if req is not None:\n", "            if not req.keepAlive:\n", "                req.done.set()\n", "        else:\n", "            raise Exception(\"historicalDataEnd without request\")\n", "\n", "\n", "\n", "    @RequestWarper\n", "    def reqRealTimeBars(self, reqId, contract, barSize, whatToShow, useRTH, realTimeBarsOptions):\n", "        req = self.register_requests( REQUEST_TYPES.GET_REAL_TIME_BARS, reqId, keepAlive=True)\n", "        super().reqRealTimeBars(reqId, contract, barSize, whatToShow, useRTH, realTimeBarsOptions)\n", "        return req\n", "\n", "\n", "    def realTimeBar(self, reqId: int, time: int, open_: float, high: float, low: float, close: float, volume: int, wap: float, count: int):\n", "        Logger.log(f\"realTimeBar. reqId: {reqId}, time: {time}, open_: {open_}, high: {high}, low: {low}, close: {close}, volume: {volume}, wap: {wap}, count: {count}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)\n", "        req.response.contents.append({\"time\": time, \"open\": open_, \"high\": high, \"low\": low, \"close\": close, \"volume\": volume, \"wap\": wap, \"count\": count})\n", "\n", "    def realtimeBarEnd(self, reqId: int):\n", "        Logger.log(f\"realtimeBarEnd. reqId: {reqId}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"realtimeBarEnd without request\") \n", "\n", "    @RequestWarper\n", "    def cancelRealTimeBars(self, reqId):\n", "        req = self.register_requests( REQUEST_TYPES.GET_REAL_TIME_BARS, reqId, timeout=2)\n", "        super().cancelRealTimeBars(reqId)\n", "        return req\n", "\n", "\n", "\n", "    @RequestWarper\n", "    def reqMktData(self, reqId: int, contract: Contract, genericTickList: str, snapshot: bool, regulatorySnapshot: bool, mktDataOptions: ListOfTagValue):\n", "        req = self.register_requests(REQUEST_TYPES.GET_MARKET_DATA, reqId, keepAlive=True)\n", "        super().reqMktData( reqId, contract, genericTickList, snapshot, regulatorySnapshot, mktDataOptions)\n", "        return req\n", "\n", "    def tickPrice(self, reqId: int, tickType: int, price: float, attrib: TickAttrib):\n", "        Logger.log(f\"tickPrice. reqId: {reqId}, tickType: {tickType}, price: {price}, attrib: {attrib}\")\n", "\n", "    \n", "    def tickSize(self, reqId: int, tickType: int, size: int):\n", "        Logger.log(f\"tickSize. reqId: {reqId}, tickType: {tickType}, size: {size}\")\n", "\n", "\n", "\n", "    def tickString(self, reqId: int, tickType: int, value: str):\n", "        Logger.log(f\"tickString. reqId: {reqId}, tickType: {tickType}, value: {value}\")\n", "\n", "    \n", "    def tickGeneric(self, reqId: int, tickType: int, value: float):\n", "        Logger.log(f\"tickGeneric. reqId: {reqId}, tickType: {tickType}, value: {value}\")\n", "\n", "\n", "    def tickEFP(self, reqId: int, tickType: int, basisPoints: float, formattedBasisPoints: str,\n", "                impliedFuture: float, holdDays: int, futureExpiry: str, dividendImpact: float,\n", "                dividendsToExpiry: float):\n", "        Logger.log(f\"tickEFP. reqId: {reqId}, tickType: {tickType}, basisPoints: {basisPoints}, formattedBasisPoints: {formattedBasisPoints}, impliedFuture: {impliedFuture}, holdDays: {holdDays}, futureExpiry: {futureExpiry}, dividendImpact: {dividendImpact}, dividendsToExpiry: {dividendsToExpiry}\")\n", "\n", "    def tickSnapshotEnd(self, reqId: int):\n", "        Logger.log(f\"tickSnapshotEnd. reqId: {reqId}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DATA],reqId)\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"tickSnapshotEnd without request\") \n", "\n", "    def tickOptionComputation(self, reqId, tickType, tickAttrib, impliedVol, delta, optPrice, pvDividend, gamma, vega, theta, undPrice):\n", "        Logger.log(f\"tickOptionComputation. reqId: {reqId}, tickType: {tickType}, tickAttrib: {tickAttrib}, impliedVol: {impliedVol}, delta: {delta}, optPrice: {optPrice}, pvDividend: {pvDividend}, gamma: {gamma}, vega: {vega}, theta: {theta}, undPrice: {undPrice}\")\n", "    \n", "    \n", "\n", "    @RequestWarper\n", "    def cancelMktData(self, reqId):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_MARKET_DATA, reqId, timeout=2)\n", "        super().cancelMktData(reqId)\n", "        return req\n", "\n", "\n", "    @RequestWarper\n", "    def reqHistoricalTicks(self, reqId, contract, startDateTime, endDateTime, nTicks, whatToShow, useRTH, ignoreSize, miscOptions):\n", "        req = self.register_requests(REQUEST_TYPES.GET_HISTORICAL_TICKS, reqId)\n", "        super().reqHistoricalTicks(reqId, contract, startDateTime, endDateTime, nTicks, whatToShow, useRTH, ignoreSize, miscOptions)\n", "        return req\n", "    \n", "\n", "    def historicalTicks(self, reqId: int, ticks: ListOfHistoricalTick, done: bool):\n", "        Logger.log(f\"historicalTicks. reqId: {reqId}, ticks: {ticks}, done: {done}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)\n", "        req.response.contents.append(ticks)\n", "\n", "    def historicalTicksLast(self, reqId: int, ticks: ListOfHistoricalTickLast, done: bool):\n", "        Logger.log(f\"historicalTicksLast. reqId: {reqId}, ticks: {ticks}, done: {done}\")\n", "\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)\n", "        req.response.contents.append(ticks)\n", "\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"historicalTicksLast without request\") \n", "\n", "    @RequestWarper\n", "    def cancelHistoricalTicks(self, reqId):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_HISTORICAL_TICKS, reqId, timeout=2)\n", "        super().cancelHistoricalTicks(reqId)\n", "        return req\n", "\n", "\n", "    @RequestWarper\n", "    def reqTickByTickData(self, reqId, contract, tickType, numberOfTicks, ignoreSize):\n", "        req = self.register_requests(REQUEST_TYPES.GET_TICK_BY_TICK_DATA, reqId, keepAlive=True)\n", "        super().reqTickByTickData(reqId, contract, tickType, numberOfTicks, ignoreSize)\n", "        return req\n", "\n", "\n", "    def tickByTickAllLast(self, reqId: int, tickType: int, time: int, price: float,\n", "                          size: int, tickAttribLast: TickAttribLast,\n", "                          exchange: str, specialConditions: str):\n", "        Logger.log(f\"tickByTickAllLast. reqId: {reqId}, tickType: {tickType}, time: {time}, price: {price}, size: {size}, tickAttribLast: {tickAttribLast}, exchange: {exchange}, specialConditions: {specialConditions}\")  \n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_TICK_BY_TICK_DATA],reqId)\n", "        req.response.contents.append({\"tickType\": tickType, \"time\": time, \"price\": price, \"size\": size, \"tickAttribLast\": tickAttribLast, \"exchange\": exchange, \"specialConditions\": specialConditions})\n", "\n", "    \n", "    @RequestWarper\n", "    def cancelTickByTickData(self, reqId):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_TICK_BY_TICK_DATA, reqId, timeout=2)\n", "        super().cancelTickByTickData(reqId)\n", "        return req\n", "\n", "\n", "    @RequestWarper\n", "    def reqMktDepth(self, reqId, contract, numRows, isSmartDepth):\n", "        req = self.register_requests(REQUEST_TYPES.GET_MARKET_DEPTH, reqId, keepAlive=True)\n", "        super().reqMktDepth(reqId, contract, numRows, isSmartDepth)\n", "        return req\n", "    \n", "    def updateMktDepth(self, reqId: int, position: int, operation: int,\n", "                        side: int, price: float, size: int):\n", "        Logger.log(f\"updateMktDepth. reqId: {reqId}, position: {position}, operation: {operation}, side: {side}, price: {price}, size: {size}\")\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DEPTH],reqId)\n", "        req.response.contents.append({\"position\": position, \"operation\": operation, \"side\": side, \"price\": price, \"size\": size})\n", "\n", "\n", "    @RequestWarper\n", "    def cancelMktDepth(self, reqId):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_MARKET_DEPTH, reqId, timeout=2)\n", "        super().cancelMktDepth(reqId)\n", "        return req\n", "    \n", "\n", "    @RequestWarper\n", "    def reqMktDepthL2(self, reqId, contract, numRows, isSmartDepth):\n", "        req = self.register_requests(REQUEST_TYPES.GET_MARKET_DEPTH_L2, reqId, keepAlive=True)\n", "        super().reqMktDepthL2(reqId, contract, numRows, isSmartDepth)\n", "        return req\n", "\n", "    def updateMktDepthL2(self, reqId: int, position: int, marketMaker: str,\n", "                        operation: int, side: int, price: float, size: int):\n", "        Logger.log(f\"updateMktDepthL2. reqId: {reqId}, position: {position}, marketMaker: {marketMaker}, operation: {operation}, side: {side}, price: {price}, size: {size}\")\n", "        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DEPTH_L2],reqId)\n", "        req.response.contents.append({\"position\": position, \"marketMaker\": marketMaker, \"operation\": operation, \"side\": side, \"price\": price, \"size\": size})    \n", "    \n", "    @RequestWarper\n", "    def cancelMktDepthL2(self, reqId):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_MARKET_DEPTH_L2, reqId, timeout=2)\n", "        super().cancelMktDepthL2(reqId)\n", "        return req\n", "\n", "\n", "    @RequestWarper\n", "    def reqAccountUpdates(self, accountCode: str):\n", "        req = self.register_requests(REQUEST_TYPES.UPDATE_ACCOUNT, keepAlive=True )\n", "        super().reqAccountUpdates(True, accountCode)\n", "        return req\n", "\n", "    \n", "    @RequestWarper\n", "    def cancelAccountUpdates(self, accountCode: str):\n", "        req = self.find_request(REQUEST_TYPES.UPDATE_ACCOUNT)\n", "        if req is None:\n", "            self.unregister_requests(req,-1)\n", "\n", "\n", "        req2 = self.register_requests(REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT, timeout=2 , noResponse=True)\n", "        super().reqAccountUpdates(False,accountCode)\n", "        \n", "        return req2\n", "\n", "    \n", "    def updatePortfolio(self, contract: Contract, position: float,\n", "                        marketPrice: float, marketValue: float,\n", "                        averageCost: float, unrealizedPNL: float,\n", "                        realizedPNL: float, accountName: str):\n", "\n", "        Logger.log(f\"updatePortfolio. contract: {contract}, position: {position}, marketPrice: {marketPrice}, marketValue: {marketValue}, averageCost: {averageCost}, unrealizedPNL: {unrealizedPNL}, realizedPNL: {realizedPNL}, accountName: {accountName}\")\n", "        \n", "        if accountName not in self.protfolioData:\n", "            self.protfolioData[accountName] = {}\n", "        \n", "        self.protfolioData[accountName][contract.conId] = {'contract': contract, 'position': position, 'marketPrice': marketPrice, 'marketValue': marketValue, 'averageCost': averageCost, 'unrealizedPNL': unrealizedPNL, 'realizedPNL': realizedPNL, 'accountName': accountName}\n", "         \n", "        \n", "\n", "    def updateAccountValue(self, key: str, val: str, currency: str,\n", "                          accountName: str):\n", "        \n", "        Logger.log(f\"updateAccountValue. key: {key}, val: {val}, currency: {currency}, accountName: {accountName}\")\n", "\n", "        if accountName not in self.accountData:\n", "            self.accountData[accountName] = {}\n", "        \n", "        self.accountData[accountName][key] = {'val': val, 'currency': currency, 'accountName': accountName}\n", "       \n", "\n", "   \n", "\n", "    def updateAccountTime(self, timeStamp: str):\n", "        Logger.log(f\"updateAccountTime. timeStamp: {timeStamp}\")\n", "        self.accountUpdateTime = timeStamp\n", "\n", " \n", "\n", "\n", "    def accountDownloadEnd(self, accountName: str):\n", "        Logger.log(f\"accountDownloadEnd. accountName: {accountName}\")\n", "       \n", "        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT])\n", "        if req is not None:\n", "            req.response.contents.append({\"accountData\": self.accountData, \"protfolioData\": self.protfolioData, \"accountUpdateTime\": self.accountUpdateTime})\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"accountDownloadEnd without request\")\n", "        \n", "\n", "    @RequestWarper\n", "    def reqAccountSummary(self, reqId: int, groupName: str, tags: str):\n", "        req = self.register_requests(REQUEST_TYPES.GET_ACCOUNT_SUMMARY, reqId, timeout=10, keepAlive=True)\n", "        super().reqAccountSummary(self, reqId, groupName, tags)\n", "        return req\n", "\n", "\n", "    def accountSummary(self, reqId: int, account: str, tag: str, value: str,\n", "                      currency: str):\n", "        Logger.log(f\"accountSummary. reqId: {reqId}, account: {account}, tag: {tag}, value: {value}, currency: {currency}\")\n", "\n", "        if account not in self.accountSummaryData:\n", "            self.accountSummaryData[account] = {}\n", "        \n", "        self.accountSummaryData[account][tag] = {'value': value, 'currency': currency}\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_ACCOUNT_SUMMARY],reqId)\n", "        if req is not None:\n", "            req.response.contents.append(self.accountSummaryData)\n", "        else:\n", "            raise Exception(\"accountSummary without request\")\n", "\n", "\n", "\n", "    def accountSummaryEnd(self, reqId: int):\n", "        Logger.log(f\"accountSummaryEnd. reqId: {reqId}\")\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_ACCOUNT_SUMMARY],reqId)\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"accountSummaryEnd without request\")\n", "\n", "\n", "    @RequestWarper\n", "    def cancelAccount<PERSON><PERSON><PERSON><PERSON>(self, reqId: int):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_ACCOUNT_SUMMARY, reqId, timeout=2)\n", "        super().cancelAccountSummary(reqId)\n", "        return req\n", "\n", "\n", "\n", "\n", "\n", "    @RequestWarper\n", "    def reqPositions(self):\n", "        req = self.register_requests(REQUEST_TYPES.GET_POSITIONS, keepAlive=True)\n", "        super().reqPositions(self)\n", "        return req\n", "    \n", "\n", "    def position(self, account: str, contract: Contract, pos: float,\n", "                avgCost: float):\n", "        Logger.log(f\"position. account: {account}, contract: {contract}, pos: {pos}, avgCost: {avgCost}\")\n", "\n", "        if account not in self.positionData:\n", "            self.positionData[account] = {}\n", "                \n", "        self.positionData[account][contract.conId] = {'contract': contract, 'pos': pos, 'avgCost': avgCost}\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_POSITIONS])\n", "        if req is not None:\n", "            req.response.contents.append(self.positionData)\n", "        else:\n", "            raise Exception(\"position without request\")\n", "\n", "        \n", "    def positionEnd(self):\n", "        Logger.log(f\"positionEnd\")\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_POSITIONS])\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"positionEnd without request\")\n", "\n", "\n", "    @RequestWarper\n", "    def cancelPositions(self):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_POSITIONS, timeout=2)\n", "        super().cancelPositions()\n", "        return req\n", "\n", "\n", "    def nextValidId(self, orderId: int):\n", "        Logger.log(f\"nextValidId. orderId: {orderId}\")\n", "        if orderId <= TickerID_RULES.REQUEST_ID_MAX:\n", "            self.nextOrderId = TickerID_RULES.REQUEST_ID_MAX + 1\n", "        else:\n", "            self.nextOrderId = orderId\n", "      \n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_IDS, REQUEST_TYPES.PLACE_ORDER],orderId)\n", "        if req is not None:\n", "            req.done.set()\n", "\n", "        req2 = self.find_request_by_type_list([REQUEST_TYPES.CONNECT])\n", "        if req2 is not None:\n", "            req2.done.set()\n", "\n", "        \n", "    \n", "\n", "    @RequestWarper\n", "    def reqIds(self, numIds):\n", "        req = self.register_requests(REQUEST_TYPES.GET_IDS, timeout=5)\n", "        super().reqIds(numIds)\n", "        return req\n", "\n", "\n", "    @RequestWarper\n", "    def reqCompletedOrders(self, apiOnly):\n", "        req = self.register_requests(REQUEST_TYPES.GET_COMPLETED_ORDERS)\n", "        super().reqCompletedOrders(apiOnly)\n", "        return req\n", "\n", "    def completedOrders(self, contract: Contract, order: Order, orderState: OrderState):\n", "        Logger.log(f\"completedOrders. contract: {contract}, order: {order}, orderState: {orderState}\")\n", "\n", "        self.completedOrdersData[order.orderId] =  {\"contract\": contract, \"order\": order, \"orderState\": orderState}\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_COMPLETED_ORDERS])\n", "        if req is not None:\n", "            req.response.contents.append(self.completedOrdersData)\n", "      \n", "        else:\n", "            raise Exception(\"completedOrders without request\")\n", "\n", "\n", "    def completedOrdersEnd(self):\n", "        Logger.log(f\"completedOrdersEnd\")\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_COMPLETED_ORDERS])\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"completedOrdersEnd without request\")\n", "\n", "\n", "    @RequestWarper\n", "    def reqcurrentTime(self):\n", "        req = self.register_requests(REQUEST_TYPES.GET_CURRENT_TIME, timeout=5)\n", "        super().reqCurrentTime()\n", "        return req\n", "\n", "\n", "    def currentTime(self, time: int):\n", "        Logger.log(f\"currentTime. time: {time}\")\n", "        self.currentTime = time\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CURRENT_TIME])\n", "        if req is not None:\n", "            req.done.set()\n", "        else:\n", "            raise Exception(\"currentTime without request\")\n", "        \n", "\n", "    @RequestWarper\n", "    def reqFundamentalData(self, reqId, contract, reportType, fundamentalDataOptions):\n", "        req = self.register_requests(REQUEST_TYPES.GET_FUNDAMENTAL_DATA, reqId, timeout=10)\n", "        super().reqFundamentalData(reqId, contract, reportType, fundamentalDataOptions)\n", "        return req\n", "    \n", "    def fundamentalData(self, reqId: int, data: str):\n", "        Logger.log(f\"fundamentalData. reqId: {reqId}, data: {data}\")\n", "\n", "        req = self.find_request_by_type_list([REQUEST_TYPES.GET_FUNDAMENTAL_DATA],reqId)\n", "        if req is not None:\n", "            req.response.contents.append(data)\n", "        else:\n", "            raise Exception(\"fundamentalData without request\")\n", "        \n", "\n", "    @RequestWarper\n", "    def cancelFundamentalData(self, reqId: int):\n", "        req = self.register_requests(REQUEST_TYPES.CANCEL_FUNDAMENTAL_DATA, reqId, timeout=10)\n", "        super().cancelFundamentalData(reqId)\n", "        return req\n", "    \n", "\n", "\n", "   \n", "    def connect(self, host, port, clientId):\n", "        req = self.register_requests(REQUEST_TYPES.CONNECT, timeout=10 ,delayWhenFinished=0.1)\n", "        super().connect(host, port, clientId)\n", "        return req\n", "\n", "\n", "    def reconnect(self):\n", "        attempt = 0\n", "        isConnect  = False\n", "        self.TWS_connection = True\n", "        while isConnect == False and attempt < 10:\n", "            Logger.log(\"Attempt Connecting...\")\n", "\n", "            super().disconnect()\n", "            time.sleep(1)\n", "\n", "            req = self.connect(self.config['host'], self.config['port'], self.config['clientId'])\n", "\n", "           \n", "            thread_run= Thread(target=super().run,args=(), daemon=True)\n", "            thread_run.start()            \n", "\n", "            req.done.wait()\n", "     \n", "            isConnect = super().isConnected()\n", "            self.TWS_connection = isConnect\n", "\n", "\n", "            Logger.log(f\"Connection attempt {attempt} : {[\"Not Success\",\"Success\"][isConnect]}\")   \n", "\n", "   \n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c1eca2db", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:reconnect <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:25</span> Attempt Connecting<span style=\"color: #808000; text-decoration-color: #808000\">...</span>\n", "</pre>\n"], "text/plain": ["caller:reconnect \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:25\u001b[0m Attempt Connecting\u001b[33m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526364</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EA7AF170</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m openOrder. orderId: \u001b[1;36m47\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m47\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526364\u001b[0m: MKT BUY \u001b[1;36m3\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EA7AF170\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526364</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m orderStatus. orderId: \u001b[1;36m47\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m3\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526364\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526361</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBB4DF10</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m openOrder. orderId: \u001b[1;36m44\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m44\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526361\u001b[0m: MKT BUY \u001b[1;36m1\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBB4DF10\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526361</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m orderStatus. orderId: \u001b[1;36m44\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m1\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526361\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">43</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">43</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526360</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBB4F140</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m openOrder. orderId: \u001b[1;36m43\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m43\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526360\u001b[0m: MKT BUY \u001b[1;36m1\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBB4F140\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">43</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526360</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m orderStatus. orderId: \u001b[1;36m43\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m1\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526360\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526362</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBB4F590</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m openOrder. orderId: \u001b[1;36m45\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m45\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526362\u001b[0m: MKT BUY \u001b[1;36m3\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBB4F590\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526362</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m orderStatus. orderId: \u001b[1;36m45\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m3\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526362\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-4</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">265598</span>,AAPL,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,AAPL,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-4</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">492222667</span>: LMT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span> GTC, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBB99670</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m openOrder. orderId: \u001b[1;36m-4\u001b[0m, contract: \n", "\u001b[1;36m265598\u001b[0m,AAPL,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,AAPL,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m-4\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m492222667\u001b[0m: LMT BUY \u001b[1;36m1\u001b[0m@\u001b[1;36m150\u001b[0m GTC, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBB99670\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-4</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">492222667</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m orderStatus. orderId: \u001b[1;36m-4\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m1\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m492222667\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrderEnd <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> openOrderEnd\n", "</pre>\n"], "text/plain": ["caller:openOrderEnd \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m openOrderEnd\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:nextValidId <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> nextValidId. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48</span>\n", "</pre>\n"], "text/plain": ["caller:nextValidId \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m nextValidId. orderId: \u001b[1;36m48\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:usfarm.nj contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:usfarm.nj contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:jfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:jfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:cafarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:cafarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:cashfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:cashfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:usfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:usfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:euhmds contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:euhmds contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:fundfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:fundfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:ushmds contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:ushmds contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2158</span>, errorString: Sec-def data farm connection is \n", "OK:secdefil contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2158\u001b[0m, errorString: Sec-def data farm connection is \n", "OK:secdefil contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">unregister_requests_without_KeepAlive. \n", "</pre>\n"], "text/plain": ["unregister_requests_without_KeepAlive. \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">unregister_requests_without_KeepAlive. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, type: CONNECT  type2: CONNECT reqId2: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>\n", "</pre>\n"], "text/plain": ["unregister_requests_without_KeepAlive. reqId: \u001b[1;36m-1\u001b[0m, type: CONNECT  type2: CONNECT reqId2: \u001b[1;36m-1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:reconnect <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:24:26</span> Connection attempt <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> : Success\n", "</pre>\n"], "text/plain": ["caller:reconnect \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:24:26\u001b[0m Connection attempt \u001b[1;36m0\u001b[0m : Success\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">finish\n", "</pre>\n"], "text/plain": ["finish\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Exception in thread Thread-6 (run):\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py\", line 1073, in _bootstrap_inner\n", "    self.run()\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\ipykernel\\ipkernel.py\", line 766, in run_closure\n", "    _threading_Thread_run(self)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py\", line 1010, in run\n", "    self._target(*self._args, **self._kwargs)\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\ibapi-10.30.1-py3.12.egg\\ibapi\\client.py\", line 398, in run\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\ibapi-10.30.1-py3.12.egg\\ibapi\\decoder.py\", line 1506, in interpret\n", "  File \"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\ibapi-10.30.1-py3.12.egg\\ibapi\\decoder.py\", line 1388, in processErrorMsg\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27868\\871131574.py\", line 258, in error\n", "AttributeError: 'NoneType' object has no attribute 'keepAlive'\n"]}], "source": ["import json \n", "\n", "class TradeTool():\n", "    def __init__(self):\n", "        self.config = 'config.json'\n", "        self.ib = None\n", "\n", "        with open(self.config) as f:\n", "            self.config = json.load(f)\n", "\n", "        self.logger = Logger(self.config)\n", "       \n", "    \n", "    def start(self):\n", "        \n", "        self.ib =  IBTool(self.config)\n", "        self.ib.reconnect()\n", "        \n", "\n", "\n", "        # con = Contract()\n", "        # con.symbol = \"TSLA\"\n", "        # con.secType = \"STK\"\n", "        # con.exchange = \"SMART\"\n", "        # con.currency = \"USD\"\n", "        # con.primaryExchange = \"NASDAQ\"\n", "\n", "        # response = self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])\n", "        # response.done.wait()\n", "    \n", "        # print(df( response.contents))\n", "        \n", "        #response = self.ib.reqAccountSummary(reqId=self.ib.reqId, groupName=\"All\", tags=\"TotalCashValue,NetLiquidation\")\n", "        #response.done.wait()\n", "        #aa = self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])\n", "        \n", "        #bb = self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"3000 S\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n", "        \n", "        #self.ib.reqAllOpenOrders().wait()\n", "\n", "\n", "        # response =self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n", "        \n", "        # # response = self.ib.reqAllOpenOrders()\n", "        # # response.done.wait()\n", "        # # response = self.ib.reqAllOpenOrders()\n", "        # response.done.wait()\n", "\n", "\n", "        # time.sleep(10000)\n", "   \n", "        print(\"finish\")\n", "\n", "\n", "\n", "            \n", "tt = TradeTool()\n", "tt.start()\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "cce65584", "metadata": {}, "outputs": [], "source": ["con = Contract()\n", "con.symbol = \"TSLA\" \n", "con.secType = \"STK\"\n", "con.exchange = \"SMART\"\n", "con.currency = \"USD\"\n", "con.primaryExchange = \"NASDAQ\"\n", "\n", "order = Order()\n", "order.action = \"BUY\"\n", "order.orderType = \"LMT\"\n", "order.lmtPrice = 222\n", "order.totalQuantity = 3\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "c1f6b260", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.TradeTool</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208E9EF1100</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m<\u001b[0m\u001b[1;95m__main__.TradeTool\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208E9EF1100\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(tt)"]}, {"cell_type": "code", "execution_count": 22, "id": "d56bc409", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:wrapper <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:54</span> RequestWrapper cancelAccountUpdates : <span style=\"font-weight: bold\">()</span> <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'accountCode'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">''</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["caller:wrapper \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:54\u001b[0m RequestWrapper cancelAccountUpdates : \u001b[1m(\u001b[0m\u001b[1m)\u001b[0m \u001b[1m{\u001b[0m\u001b[32m'accountCode'\u001b[0m: \u001b[32m''\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:54</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2100</span>, errorString: API client has been unsubscribed \n", "from account data. contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:54\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2100\u001b[0m, errorString: API client has been unsubscribed \n", "from account data. contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">unregister_requests_without_KeepAlive. \n", "</pre>\n"], "text/plain": ["unregister_requests_without_KeepAlive. \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">unregister_requests_without_KeepAlive. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, type: CANCEL_UPDATE_ACCOUNT  type2: CANCEL_UPDATE_ACCOUNT reqId2:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>\n", "</pre>\n"], "text/plain": ["unregister_requests_without_KeepAlive. reqId: \u001b[1;36m-1\u001b[0m, type: CANCEL_UPDATE_ACCOUNT  type2: CANCEL_UPDATE_ACCOUNT reqId2:\n", "\u001b[1;36m-1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["False"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# req = tt.ib.reqHistoricalData(reqId=1, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"5 mins\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])\n", "# req.done.wait()\n", "\n", "req2 = tt.ib.cancelAccountUpdates(accountCode=\"\")\n", "req2.done.wait()"]}, {"cell_type": "code", "execution_count": 20, "id": "59375a7a", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:wrapper <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> RequestWrapper reqAccountUpdates : <span style=\"font-weight: bold\">()</span> <span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'accountCode'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">''</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["caller:wrapper \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m RequestWrapper reqAccountUpdates : \u001b[1m(\u001b[0m\u001b[1m)\u001b[0m \u001b[1m{\u001b[0m\u001b[32m'accountCode'\u001b[0m: \u001b[32m''\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccountCode, val: DU7492998, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccountCode, val: DU7492998, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: \n", "USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: \n", "USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccountReady, val: true, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccountReady, val: true, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccountType, val: INDIVIDUAL, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccountType, val: INDIVIDUAL, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccruedCash, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccruedCash, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccruedCash, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccruedCash, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccruedCash, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccruedCash, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccruedCash-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccruedCash-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccruedDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccruedDividend, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AccruedDividend-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AccruedDividend-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1057138.28</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AvailableF<PERSON>, val: \u001b[1;36m1057138.28\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: AvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1057138.28</span>, currency:\n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: AvailableFunds-S, val: \u001b[1;36m1057138.28\u001b[0m, currency:\n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Billable, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Billable, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Billable-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Billable-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: BuyingPower, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7047588.53</span>, currency: HKD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: BuyingPower, val: \u001b[1;36m7047588.53\u001b[0m, currency: HKD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: CashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">761959.2043</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: CashBalance, val: \u001b[1;36m761959.2043\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: CashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">388596.66</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: CashBalance, val: \u001b[1;36m388596.66\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: CashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47578.44</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: CashBalance, val: \u001b[1;36m47578.44\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: ColumnPrio-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: ColumnPrio-S, val: \u001b[1;36m1\u001b[0m, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: CorporateBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: CorporateBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: CorporateBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: CorporateBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: CorporateBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: CorporateBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Cryptocurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Cryptocurrency, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Cryptocurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Cryptocurrency, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Cryptocurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Cryptocurrency, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Currency, val: BASE, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Currency, val: BASE, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Currency, val: HKD, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Currency, val: HKD, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Currency, val: USD, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Currency, val: USD, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Cushion, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.945721</span>, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: <PERSON><PERSON><PERSON>, val: \u001b[1;36m0.945721\u001b[0m, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: EquityWithLoanValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1128571.14</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: EquityWithLoan<PERSON><PERSON><PERSON>, val: \u001b[1;36m1128571.14\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: EquityWithLoanValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1128571.14</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: EquityWithLoanValue-S, val: \u001b[1;36m1128571.14\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: ExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1069192.71</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: ExcessLiquidity, val: \u001b[1;36m1069192.71\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: ExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1069192.71</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: ExcessLiquidity-S, val: \u001b[1;36m1069192.71\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: ExchangeRate, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: ExchangeRate, val: \u001b[1;36m1.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: ExchangeRate, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: ExchangeRate, val: \u001b[1;36m1.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: ExchangeRate, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.84692</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: ExchangeRate, val: \u001b[1;36m7.84692\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullAvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1057138.28</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullAvailableFunds, val: \u001b[1;36m1057138.28\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullAvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1057138.28</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullAvailableFunds-S, val: \u001b[1;36m1057138.28\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1069192.71</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullExcessLiquidity, val: \u001b[1;36m1069192.71\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1069192.71</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullExcessLiquidity-S, val: \u001b[1;36m1069192.71\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullInitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">71432.86</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullInitMarginReq, val: \u001b[1;36m71432.86\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullInitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">71432.86</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullInitMarginReq-S, val: \u001b[1;36m71432.86\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullMaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61365.77</span>, currency:\n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullMaintMarginReq, val: \u001b[1;36m61365.77\u001b[0m, currency:\n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FullMaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61365.77</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FullMaintMarginReq-S, val: \u001b[1;36m61365.77\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FundValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FundValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FundValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FutureOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FutureOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FutureOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FutureOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FutureOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FutureOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FuturesPNL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FuturesPNL, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FuturesPNL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FuturesPNL, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FuturesPNL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FuturesPNL, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FxCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FxCashBalance, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FxCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FxCashBalance, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: FxCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: FxCashBalance, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: GrossPositionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">369540.67</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: GrossPositionValue, val: \u001b[1;36m369540.67\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: GrossPositionValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">369540.67</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: GrossPositionValue-S, val: \u001b[1;36m369540.67\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: <PERSON>uarantee, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: <PERSON><PERSON><PERSON><PERSON>, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Guarantee-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Guarantee-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IncentiveCoupons, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Incentive<PERSON><PERSON><PERSON><PERSON>, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IncentiveCoupons-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: IncentiveCoupons-S, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IndianStockHaircut, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: IndianStockHaircut, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IndianStockHaircut-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: IndianStockHaircut-S, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: InitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">71432.86</span>, currency: HKD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: InitMarginReq, val: \u001b[1;36m71432.86\u001b[0m, currency: HKD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: InitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">71432.86</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: InitMarginReq-S, val: \u001b[1;36m71432.86\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IssuerOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: IssuerOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IssuerOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: IssuerOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: IssuerOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: IssuerOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: Leverage-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.33</span>, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Leverage-S, val: \u001b[1;36m0.33\u001b[0m, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadAvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1057138.28</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadAvailableFunds, val: \u001b[1;36m1057138.28\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadAvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1057138.28</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadAvailableFunds-S, val: \u001b[1;36m1057138.28\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1069192.71</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadExcessLiquidity, val: \u001b[1;36m1069192.71\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1069192.71</span>,\n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadExcessLiquidity-S, val: \u001b[1;36m1069192.71\u001b[0m,\n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadInitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">71432.86</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadInitMarginReq, val: \u001b[1;36m71432.86\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadInitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">71432.86</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadInitMarginReq-S, val: \u001b[1;36m71432.86\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadMaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61365.77</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadMaintMarginReq, val: \u001b[1;36m61365.77\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadMaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61365.77</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: LookAheadMaintMarginReq-S, val: \u001b[1;36m61365.77\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: LookAheadNextChange, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">**********</span>, \n", "currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: Look<PERSON><PERSON><PERSON>ext<PERSON><PERSON><PERSON>, val: \u001b[1;36m**********\u001b[0m, \n", "currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61365.77</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MaintMarginReq, val: \u001b[1;36m61365.77\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61365.77</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MaintMarginReq-S, val: \u001b[1;36m61365.77\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MoneyMarketFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MoneyMarketFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MoneyMarketFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MoneyMarketFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MoneyMarketFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MoneyMarketFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MutualFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MutualFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MutualFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MutualFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: MutualFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: MutualFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NLVAndMarginInReview, val: false, currency: \n", ", accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NLVAndMarginInReview, val: false, currency: \n", ", accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetDividend, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetDividend, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetDividend, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetLiquidation, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1130558.48</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetLiquidation, val: \u001b[1;36m1130558.48\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetLiquidation-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1130558.48</span>, currency:\n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetLiquidation-S, val: \u001b[1;36m1130558.48\u001b[0m, currency:\n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1130558.479</span>, \n", "currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m1130558.479\u001b[0m, \n", "currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">388596.9366</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m388596.9366\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">94549.8504</span>, \n", "currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m94549.8504\u001b[0m, \n", "currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: NetLiquidationUncertainty, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1987.34</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: NetLiquidationUncertainty, val: \u001b[1;36m1987.34\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: OptionMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: OptionMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: OptionMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: OptionMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: OptionMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: OptionMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PASharesValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PASharesValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PASharesValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PASharesValue-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PhysicalCertificateValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PhysicalCertificateValue, val: \u001b[1;36m0.00\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PhysicalCertificateValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PhysicalCertificateValue-S, val: \u001b[1;36m0.00\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PostExpirationExcess, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PostExpirationExcess, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PostExpirationExcess-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency:\n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PostExpirationExcess-S, val: \u001b[1;36m0.00\u001b[0m, currency:\n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PostExpirationMargin, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PostExpirationMargin, val: \u001b[1;36m0.00\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: PostExpirationMargin-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency:\n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: PostExpirationMargin-S, val: \u001b[1;36m0.00\u001b[0m, currency:\n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: RealCurrency, val: BASE, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: RealCurrency, val: BASE, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: RealCurrency, val: HKD, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: RealCurrency, val: HKD, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: RealCurrency, val: USD, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: RealCurrency, val: USD, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: RealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: RealizedPnL, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: RealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: RealizedPnL, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: RealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: RealizedPnL, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: SegmentTitle-S, val: Securities, currency: ,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: SegmentTitle-S, val: Securities, currency: ,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">368599.00</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: StockMarketValue, val: \u001b[1;36m368599.00\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: StockMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">46971.41</span>, currency: \n", "USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: StockMarketValue, val: \u001b[1;36m46971.41\u001b[0m, currency: \n", "USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TBillValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TBillValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TBillValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TBillValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TBillValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TBillValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">761959.2043</span>, \n", "currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalCashBalance, val: \u001b[1;36m761959.2043\u001b[0m, \n", "currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">388596.66</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalCashBalance, val: \u001b[1;36m388596.66\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47578.44</span>, currency: \n", "USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalCashBalance, val: \u001b[1;36m47578.44\u001b[0m, currency: \n", "USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalCashValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">761959.20</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalCashValue, val: \u001b[1;36m761959.20\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalCashValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">761959.20</span>, currency: \n", "HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalCashValue-S, val: \u001b[1;36m761959.20\u001b[0m, currency: \n", "HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalDebitCardPendingCharges, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalDebitCardPendingCharges, val: \u001b[1;36m0.00\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TotalDebitCardPendingCharges-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, \n", "currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TotalDebitCardPendingCharges-S, val: \u001b[1;36m0.00\u001b[0m, \n", "currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: TradingType-S, val: STKNOPT, currency: , \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: TradingType-S, val: STKNOPT, currency: , \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">91483.05</span>, currency: \n", "BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m91483.05\u001b[0m, currency: \n", "BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.27</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m0.27\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11657.86</span>, currency: USD,\n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m11657.86\u001b[0m, currency: USD,\n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: WarrantValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: WarrantVal<PERSON>, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: WarrantValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: WarrantVal<PERSON>, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountValue <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountValue. key: WarrantValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updateAccountValue \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountValue. key: WarrantVal<PERSON>, val: \u001b[1;36m0.00\u001b[0m, currency: USD, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4815747</span>,NVDA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,NVDA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">141.8800049</span>, marketValue: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">283.76</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">105.38505</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">72.99</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m4815747\u001b[0m,NVDA,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,NVDA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \u001b[1;36m141.8800049\u001b[0m, marketValue: \n", "\u001b[1;36m283.76\u001b[0m, averageCost: \u001b[1;36m105.38505\u001b[0m, unrealizedPNL: \u001b[1;36m72.99\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,PLTR,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,PLTR,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">127.6100006</span>, marketValue:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">127.61</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.8394</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">36.77</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m*********\u001b[0m,PLTR,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,PLTR,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m1\u001b[0m, marketPrice: \u001b[1;36m127.6100006\u001b[0m, marketValue:\n", "\u001b[1;36m127.61\u001b[0m, averageCost: \u001b[1;36m90.8394\u001b[0m, unrealizedPNL: \u001b[1;36m36.77\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,QQQ,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,QQQ,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">60</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">530.6699829</span>, marketValue: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31840.2</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">371.94</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9523.8</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m*********\u001b[0m,QQQ,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,QQQ,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m60\u001b[0m, marketPrice: \u001b[1;36m530.6699829\u001b[0m, marketValue: \n", "\u001b[1;36m31840.2\u001b[0m, averageCost: \u001b[1;36m371.94\u001b[0m, unrealizedPNL: \u001b[1;36m9523.8\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,TEM,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TEM,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">62.********</span>, marketValue: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">559.8</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39.0671778</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">208.2</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m*********\u001b[0m,TEM,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TEM,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m9\u001b[0m, marketPrice: \u001b[1;36m62.********\u001b[0m, marketValue: \n", "\u001b[1;36m559.8\u001b[0m, averageCost: \u001b[1;36m39.0671778\u001b[0m, unrealizedPNL: \u001b[1;36m208.2\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TLH,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,ARCA,USD,TLH,TLH,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">98.********</span>, marketValue: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">197.12</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">102.49</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-7.86</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m********\u001b[0m,TLH,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,ARCA,USD,TLH,TLH,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \u001b[1;36m98.********\u001b[0m, marketValue: \n", "\u001b[1;36m197.12\u001b[0m, averageCost: \u001b[1;36m102.49\u001b[0m, unrealizedPNL: \u001b[1;36m-7.86\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">15:17</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m15:17\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TLT,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TLT,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">85.********</span>, marketValue: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">170.66</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">95.60305</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-20.55</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m********\u001b[0m,TLT,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TLT,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \u001b[1;36m85.********\u001b[0m, marketValue: \n", "\u001b[1;36m170.66\u001b[0m, averageCost: \u001b[1;36m95.60305\u001b[0m, unrealizedPNL: \u001b[1;36m-20.55\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">15:17</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m15:17\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">46</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">297.4700012</span>, marketValue:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">13683.62</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">257.********</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1851.06</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m46\u001b[0m, marketPrice: \u001b[1;36m297.4700012\u001b[0m, marketValue:\n", "\u001b[1;36m13683.62\u001b[0m, averageCost: \u001b[1;36m257.********\u001b[0m, unrealizedPNL: \u001b[1;36m1851.06\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,USD,CFD,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,,HKD,USD.HKD,USD.HKD,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">120</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.8473053</span>, marketValue:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">941.68</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.84504</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.27</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m*********\u001b[0m,USD,CFD,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,,HKD,USD.HKD,USD.HKD,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m120\u001b[0m, marketPrice: \u001b[1;36m7.8473053\u001b[0m, marketValue:\n", "\u001b[1;36m941.68\u001b[0m, averageCost: \u001b[1;36m7.84504\u001b[0m, unrealizedPNL: \u001b[1;36m0.27\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updatePortfolio <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updatePortfolio. contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,VGLT,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,VGLT,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54.3199997</span>, marketValue: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">108.64</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57.59525</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-6.55</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:updatePortfolio \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updatePortfolio. contract: \n", "\u001b[1;36m********\u001b[0m,VGLT,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,VGLT,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \u001b[1;36m54.3199997\u001b[0m, marketValue: \n", "\u001b[1;36m108.64\u001b[0m, averageCost: \u001b[1;36m57.59525\u001b[0m, unrealizedPNL: \u001b[1;36m-6.55\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">21:22</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m21:22\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:updateAccountTime <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:23</span>\n", "</pre>\n"], "text/plain": ["caller:updateAccountTime \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m updateAccountTime. timeStamp: \u001b[1;92m19:23\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:accountDownloadEnd <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:26:35</span> accountDownloadEnd. accountName: DU7492998\n", "</pre>\n"], "text/plain": ["caller:accountDownloadEnd \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m10:26:35\u001b[0m accountDownloadEnd. accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">unregister_requests_without_KeepAlive. \n", "</pre>\n"], "text/plain": ["unregister_requests_without_KeepAlive. \n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">unregister_requests_without_KeepAlive. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, type: UPDATE_ACCOUNT  type2: UPDATE_ACCOUNT reqId2: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>\n", "</pre>\n"], "text/plain": ["unregister_requests_without_KeepAlive. reqId: \u001b[1;36m-1\u001b[0m, type: UPDATE_ACCOUNT  type2: UPDATE_ACCOUNT reqId2: \u001b[1;36m-1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["True"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["req2 = tt.ib.reqAccountUpdates(accountCode=\"\")\n", "req2.done.wait()"]}, {"cell_type": "code", "execution_count": 24, "id": "045d6df6", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">__main__.Request</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBC4E870</span><span style=\"font-weight: bold\">&gt;]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m<\u001b[0m\u001b[1;95m__main__.Request\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBC4E870\u001b[0m\u001b[1m>\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(tt.ib.requests)"]}, {"cell_type": "code", "execution_count": 9, "id": "b4ec0dac", "metadata": {}, "outputs": [], "source": ["tt.ib.requests=[]"]}, {"cell_type": "code", "execution_count": null, "id": "47495882", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m12\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">11:45:17</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2157</span>, errorString: Sec-def data farm connection is \n", "broken:secdefil contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m11:45:17\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2157\u001b[0m, errorString: Sec-def data farm connection is \n", "broken:secdefil contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">11:45:18</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2158</span>, errorString: Sec-def data farm connection is \n", "OK:secdefil contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m11:45:18\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2158\u001b[0m, errorString: Sec-def data farm connection is \n", "OK:secdefil contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:00:32</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2105</span>, errorString: HMDS data farm connection is \n", "broken:fundfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:00:32\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2105\u001b[0m, errorString: HMDS data farm connection is \n", "broken:fundfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:00:53</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2105</span>, errorString: HMDS data farm connection is \n", "broken:fundfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:00:53\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2105\u001b[0m, errorString: HMDS data farm connection is \n", "broken:fundfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:00:54</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:fundfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:00:54\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:fundfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:01:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2105</span>, errorString: HMDS data farm connection is \n", "broken:fundfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:01:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2105\u001b[0m, errorString: HMDS data farm connection is \n", "broken:fundfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:01:26</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:fundfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:01:26\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:fundfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:02:18</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2105</span>, errorString: HMDS data farm connection is \n", "broken:ushmds contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:02:18\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2105\u001b[0m, errorString: HMDS data farm connection is \n", "broken:ushmds contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:02:27</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:ushmds contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:02:27\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:ushmds contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:04:33</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2105</span>, errorString: HMDS data farm connection is \n", "broken:euhmds contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:04:33\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2105\u001b[0m, errorString: HMDS data farm connection is \n", "broken:euhmds contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:04:35</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, errorString: HMDS data farm connection is \n", "OK:euhmds contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:04:35\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2106\u001b[0m, errorString: HMDS data farm connection is \n", "OK:euhmds contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:07</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1100</span>, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:07\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m1100\u001b[0m, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:13</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1100</span>, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:13\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m1100\u001b[0m, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:18</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1100</span>, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:18\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m1100\u001b[0m, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:23</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1100</span>, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:23\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m1100\u001b[0m, errorString: Connectivity between IBKR and \n", "Trader Workstation has been lost. contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">265598</span>,AAPL,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,AAPL,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">492222667</span>: LMT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150</span> GTC, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBC29310</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m openOrder. orderId: \u001b[1;36m0\u001b[0m, contract: \n", "\u001b[1;36m265598\u001b[0m,AAPL,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,AAPL,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m492222667\u001b[0m: LMT BUY \u001b[1;36m1\u001b[0m@\u001b[1;36m150\u001b[0m GTC, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBC29310\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">492222667</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m orderStatus. orderId: \u001b[1;36m0\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m1\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m492222667\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">43</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">43</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526360</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBC24350</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m openOrder. orderId: \u001b[1;36m43\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m43\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526360\u001b[0m: MKT BUY \u001b[1;36m1\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBC24350\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">43</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526360</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m orderStatus. orderId: \u001b[1;36m43\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m1\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526360\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526361</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBB4C1D0</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m openOrder. orderId: \u001b[1;36m44\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m44\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526361\u001b[0m: MKT BUY \u001b[1;36m1\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBB4C1D0\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526361</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m orderStatus. orderId: \u001b[1;36m44\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m1\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526361\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526362</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBC5AB40</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m openOrder. orderId: \u001b[1;36m45\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m45\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526362\u001b[0m: MKT BUY \u001b[1;36m3\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBC5AB40\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526362</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m orderStatus. orderId: \u001b[1;36m45\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m3\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526362\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:openOrder <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> openOrder. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47</span>, contract: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,?,,SMART,,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, order: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526364</span>: MKT BUY <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>@<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> DAY, orderState: \n", "<span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">ibapi.order_state.OrderState</span><span style=\"color: #000000; text-decoration-color: #000000\"> object at </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0x00000208EBC2B9E0</span><span style=\"font-weight: bold\">&gt;</span>\n", "</pre>\n"], "text/plain": ["caller:openOrder \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m openOrder. orderId: \u001b[1;36m47\u001b[0m, contract: \n", "\u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,?,,SMART,,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, order: \u001b[1;36m47\u001b[0m,\u001b[1;36m0\u001b[0m,\u001b[1;36m1234526364\u001b[0m: MKT BUY \u001b[1;36m3\u001b[0m@\u001b[1;36m0\u001b[0m DAY, orderState: \n", "\u001b[1m<\u001b[0m\u001b[1;95mibapi.order_state.OrderState\u001b[0m\u001b[39m object at \u001b[0m\u001b[1;36m0x00000208EBC2B9E0\u001b[0m\u001b[1m>\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:orderStatus <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> orderStatus. orderId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47</span>, status: PreSubmitted, filled: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, remaining: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>, \n", "avgFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, permId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1234526364</span>, parentId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, lastFillPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, clientId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, whyHeld: , mktCapPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "</pre>\n"], "text/plain": ["caller:orderStatus \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m orderStatus. orderId: \u001b[1;36m47\u001b[0m, status: PreSubmitted, filled: \u001b[1;36m0\u001b[0m, remaining: \u001b[1;36m3\u001b[0m, \n", "avgFillPrice: \u001b[1;36m0.0\u001b[0m, permId: \u001b[1;36m1234526364\u001b[0m, parentId: \u001b[1;36m0\u001b[0m, lastFillPrice: \u001b[1;36m0.0\u001b[0m, clientId: \u001b[1;36m0\u001b[0m, whyHeld: , mktCapPrice: \u001b[1;36m0.0\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:27</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1102</span>, errorString: Connectivity between IBKR and \n", "Trader Workstation has been restored - data maintained. All data farms are connected: usfarm.nj; jfarm; cafarm; \n", "cashfarm; usfarm; euhmds; fundfarm; ushmds; secdefil. contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:27\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m1102\u001b[0m, errorString: Connectivity between IBKR and \n", "Trader Workstation has been restored - data maintained. All data farms are connected: usfarm.nj; jfarm; cafarm; \n", "cashfarm; usfarm; euhmds; fundfarm; ushmds; secdefil. contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:29</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:hfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:29\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:hfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:29</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:usfuture contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:29\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:usfuture contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:17:29</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:eufarmnj contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:17:29\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:eufarmnj contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:19:45</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2103</span>, errorString: Market data farm connection is \n", "broken:cafarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:19:45\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2103\u001b[0m, errorString: Market data farm connection is \n", "broken:cafarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:19:45</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2103</span>, errorString: Market data farm connection is \n", "broken:usfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:19:45\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2103\u001b[0m, errorString: Market data farm connection is \n", "broken:usfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:19:46</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:cafarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:19:46\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:cafarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:19:47</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:usfarm contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:19:47\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:usfarm contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:22:49</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2103</span>, errorString: Market data farm connection is \n", "broken:usfarm.nj contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:22:49\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2103\u001b[0m, errorString: Market data farm connection is \n", "broken:usfarm.nj contract:\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">caller:error <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">06</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:22:51</span> error. reqId: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-1</span>, errorCode: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, errorString: Market data farm connection is \n", "OK:usfarm.nj contract:\n", "</pre>\n"], "text/plain": ["caller:error \u001b[1;36m2025\u001b[0m-\u001b[1;36m06\u001b[0m-\u001b[1;36m09\u001b[0m \u001b[1;92m12:22:51\u001b[0m error. reqId: \u001b[1;36m-1\u001b[0m, errorCode: \u001b[1;36m2104\u001b[0m, errorString: Market data farm connection is \n", "OK:usfarm.nj contract:\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(tt.ib.requests[0].type)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}