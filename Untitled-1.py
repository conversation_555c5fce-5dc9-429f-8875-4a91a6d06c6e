if __name__ == '__main__':

    from threading import Thread, Event
    import pandas as pd
    import pandas_ta as ta
    import yfinance as yf

    from lightweight_charts import Chart
    from ibapi.wrapper import EWrapper
    from ibapi.client import EClient
    import time 
    from ibapi.common import *
    from ibapi.contract import Contract
    from typing import Any
    import decimal



    class ibapp(EClient, EWrapper):
        def __init__(self):
            EClient.__init__(self, self)
            self.done = Event()  # use threading.Event to signal between threads
            self.connection_ready = Event()  # to signal the connection has been established
            self.bars = [] 

        # override Ewrapper.error
        def error(
            self, reqId: TickerId, errorCode: int, errorString: str, contract: Any = None
        ):
            print("Error: ", reqId, " ", errorCode, " ", errorString)
            if errorCode == 502:  # not connected
                # set self.done (a threading.Event) to True
                self.done.set()

        def historicalData(self, reqId, bar):
            clean_date = bar.date[:16]
            self.bars.append([clean_date, bar.open, bar.high, bar.low, bar.close, bar.volume])
            print(clean_date)
            print(bar)



    app =  ibapp()

    app.connect("127.0.0.1", 7497, clientId=0)  

    time.sleep(1)



    api_thread = Thread(target=app.run, args=(), daemon=True)
    api_thread.start()

    time.sleep(1)

    con = Contract()
    con.symbol = "TSLA"
    con.secType = "STK"
    con.exchange = "SMART"
    con.currency = "USD"
    con.primaryExchange = "NASDAQ"

    app.reqHistoricalData(0, con, "", "1 D", "1 min", "TRADES", 0, 1, False, [])   

    print(time.time())
    time.sleep(2)

    print(time.time())
    df = pd.DataFrame(app.bars, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])

    df = df.map(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)

    def get_bar_data(symbol, timeframe):
        con = Contract()
        con.symbol = symbol
        con.secType = "STK"
        con.exchange = "SMART"
        con.currency = "USD"
        con.primaryExchange = "NASDAQ"
        print(app)
        app.reqHistoricalData(0, con, "", "1 D", timeframe, "TRADES", 0, 1, False, [])   
        
        time.sleep(2)

        df = pd.DataFrame(app.bars, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])
        df = df.map(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)
        return df


    def on_search(chart, searched_string):  # Called when the user searches.
        new_data = get_bar_data(searched_string, chart.topbar['timeframe'].value)
        if new_data.empty:
            return
        chart.topbar['symbol'].set(searched_string)
        chart.set(new_data)


    def on_timeframe_selection(chart):  # Called when the user changes the timeframe.
        new_data = get_bar_data(chart.topbar['symbol'].value, chart.topbar['timeframe'].value)
        if new_data.empty:
            return
        chart.set(new_data, True)


    def on_horizontal_line_move(chart, line):
        print(f'Horizontal line moved to: {line.price}')


    chart = Chart(toolbox=True)
    chart.legend(True)

    chart.events.search += on_search

    chart.topbar.textbox('symbol', 'TSLA')
    chart.topbar.switcher('timeframe', ('1 min', '5 mins', '30 mins'), default='5min',
                            func=on_timeframe_selection)

    df = get_bar_data('TSLA', '5 mins')
    chart.set(df)

    chart.horizontal_line(200, func=on_horizontal_line_move)

    chart.show()

