
from Comm import *
import debugpy  

from threading import Thread, Event
from ibapi.order_cancel import OrderCancel
from ibapi.wrapper import EWrapper
from ibapi.client import EClient
from ibapi.common import TickerId, BarData, TickAttrib, TickAttribLast, MarketDataTypeEnum, ListOfHistoricalTick, ListOfHistoricalTickLast, WshEventData
from ibapi.contract import Contract, ContractDetails
from ibapi.order import Order
from ibapi.order_state import OrderState
from ibapi.execution import ExecutionFilter
from ibapi.commission_report import CommissionReport
from ibapi.ticktype import TickTypeEnum

import pytz

from functools import wraps

from pandas import DataFrame as df
from ibapi.tag_value import TagValue 

from typing import Any


from collections import defaultdict
from decimal import Decimal


from typing import List
ListOfTagValue = List[TagValue]

class IBTool(EClient, EWrapper):
    def __init__(self, config):
        EClient.__init__(self, self)
        self.config = config
        self.nextOrderId = 0
        self.accountSummaryData = {}
        self.accountData = {}
        self.protfolioData = {}
        self.accountUpdateTime = ""
        self.openOrdersData = {}
        self.openStatusData = {}
        self.execDetailsData = {}
        self.completedOrdersData = {}

        self.requests = []
        self.sys_current_time = 0

        self.TWS_connection = False
        self.TWS_aviliable = True
        self.thread_run = None
        self.thread_connection = None

    def register_requests(self, type: REQUEST_TYPES, reqId: int = -1, keepAlive: bool = False, timeout: float = 0.0, delayWhenFinished: float = 0.0 , noResponse: bool = False):
        if type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:
            if self.find_unduplicate_running_request( type):
                raise Exception(f"Request { self.get_request_type_name(type) }, no more than one unduplicated request allowed.")

        # if type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:
        #     if self.find_request(type, reqId) is not None:
        #         raise Exception(f"Request { self.get_request_type_name(type) } already exists and is not allowed to be duplicated.")
        


    
        req = Request(type, reqId = reqId, keepAlive = keepAlive, timeout = timeout, delayWhenFinished =  delayWhenFinished , callbackWhenFinished = self.unregister_requests_without_KeepAlive, noResponse = noResponse)
        self.requests.append(req)

        return req


    def unregister_requests(self, type: REQUEST_TYPES, reqId: int = -1):
        req = self.find_request(type, reqId)
        if req is not None:
            self.requests.remove(req)

    def unregister_requests_by_type_list(self, type_list: list[REQUEST_TYPES], reqId: int = -1):
   
        debugpy.breakpoint()
        for req in self.requests:
            if req.type in type_list and (reqId == -1 or req.reqId == reqId):
                self.requests.remove(req)

    def unregister_requests_without_KeepAlive(self, req: Request):
        type = req.type
        reqId = req.reqId

        for req in self.requests:
            if req.type == type and (reqId == -1 or req.reqId == reqId):
                
                if not req.keepAlive:
                    self.requests.remove(req)


    def find_request(self, type: REQUEST_TYPES, reqId: int = -1):
        for req in self.requests:
            if req.type == type and (reqId == -1 or req.reqId == reqId):
                return req
        return None
    
    def find_request_by_reqId(self, reqId: int):
        for req in self.requests:
            if req.reqId == reqId:
                return req
        return None
    
    def find_request_by_type_list(self, type_list: list[REQUEST_TYPES], reqId: int = -1):
        for req in self.requests:
            if req.type in type_list and (reqId == -1 or req.reqId == reqId):
                return req
        return None
    
    def find_unduplicate_running_request(self, type:REQUEST_TYPES):
        for req in self.requests:
            if  type == req.type and req.type not in REQUEST_TYPES_ALLOW_DUPLICATE.LIST:
                return req
        
        return None

    def find_last_unfinished_request(self):

        for i in range(len(self.requests)-1, -1, -1):
            if  not self.requests[i].done.is_set():
                return self.requests[i]

        return None


    def get_request_type_name(self, value):
        for name, val in REQUEST_TYPES.__dict__.items():
            if val == value:
                return name
        return None



    @staticmethod
    def RequestWarper(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):

            retry_count = 0
            max_retry = 300

            Logger.log(f"Wrapper Start Request {func.__name__} is waiting for the connection OK. isConnected:{super().isConnected()} TWS_connection: {self.TWS_connection}, TWS_aviliable: {self.TWS_aviliable}")
            while retry_count < max_retry:
                if not (self.TWS_connection  and super().isConnected() and self.TWS_aviliable) :
                    
                    if not (self.TWS_connection  and super().isConnected()):
                        if self.thread_connection is  None or not self.thread_connection.is_alive():
                            self.thread_connection = Thread(target=self.reconnect, daemon=True)
                            self.thread_connection.start()


                    if retry_count % 10 == 0:
                        Logger.log(f"Request {func.__name__} is waiting for the connection OK. isConnected:{super().isConnected()} TWS_connection: {self.TWS_connection}, TWS_aviliable: {self.TWS_aviliable}")

                    retry_count += 1
                    time.sleep(1)

                    if retry_count == max_retry:
                        req = Request(REQUEST_TYPES.REQUEST_ERROR)
                        req.response = Response()   
                        req.response.errors.append(CLIENT_REQUEST_ERRORS.CONNECTION_UNAVAILABLE)
                        print(" empty request return")
                        return req
                else:
                    break
            
            if self.thread_connection is not None and self.thread_connection.is_alive():
                self.thread_connection.join()

            Logger.log(f"RequestWrapper {func.__name__} : {args} {kwargs}")
            return func(self, *args, **kwargs)
        return wrapper

    # def cancelrequest(tyep : REQUEST_TYPES):
        

    @RequestWarper
    def placeOrder(self, contract: Contract, order: Order):
        req = self.register_requests(REQUEST_TYPES.PLACE_ORDER, self.nextOrderId,timeout=10, delayWhenFinished=0.1)
        super().placeOrder(self.nextOrderId, contract, order)
        self.nextOrderId += 1
        return req

    def openOrder(self, orderId: int, contract: Contract, order: Order, orderState: OrderState):
        Logger.log(f"openOrder. orderId: {orderId}, contract: {contract}, order: {order}, orderState: {orderState}")        
        self.openOrdersData[orderId] = {"contract": contract, "order": order, "orderState": orderState}

        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER, REQUEST_TYPES.UPDATE_ORDER,REQUEST_TYPES.GET_OPEN_ORDERS],orderId) 
        if req is not None:
            if len(req.response.contents) == 0:
                req.response.contents.append({"openOrdersData": self.openOrdersData})
            else:
                req.response.contents[0]["openOrdersData"] = self.openOrdersData

    
    def orderBound(self, reqId: int, apiClientId: int, apiOrderId: int):
        Logger.log(f"orderBound. reqId: {reqId}, apiClientId: {apiClientId}, apiOrderId: {apiOrderId}")

    def getOpenOrders(self):
        result = {}
        for openOrderItem in self.openOrdersData.values():
            if openOrderItem["orderState"].status != "PendingCancel":
                result[openOrderItem["order"].orderId] = openOrderItem
        
        return result
    
    def orderStatus(self, orderId: int, status: str, filled: float, remaining: float, avgFillPrice: float, permId: int, parentId: int, lastFillPrice: float, clientId: int, whyHeld: str, mktCapPrice: float):
        Logger.log(f"orderStatus. orderId: {orderId}, status: {status}, filled: {filled}, remaining: {remaining}, avgFillPrice: {avgFillPrice}, permId: {permId}, parentId: {parentId}, lastFillPrice: {lastFillPrice}, clientId: {clientId}, whyHeld: {whyHeld}, mktCapPrice: {mktCapPrice}")

        self.openStatusData[orderId] = {"status": status, "filled": filled, "remaining": remaining, "avgFillPrice": avgFillPrice, "permId": permId, "parentId": parentId, "lastFillPrice": lastFillPrice, "clientId": clientId, "whyHeld": whyHeld, "mktCapPrice": mktCapPrice}
        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER,REQUEST_TYPES.UPDATE_ORDER],orderId) 
        if req is not None:
            if len(req.response.contents) == 0:
                req.response.contents.append({"openStatusData": self.openStatusData})
            else:
                req.response.contents[0]["openStatusData"] = self.openStatusData
           
            req.done.set()



    #This will be callback by login successfuly, placeOrder 
    def openOrderEnd(self):
        Logger.log("openOrderEnd")
        
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_OPEN_ORDERS])
        if req is not None:
            req.response.contents.append(self.openOrdersData)
            req.done.set()

        


    #dont has call back, delete and place new order better.
    @RequestWarper
    def updateOrder(self, orderId: int, contract: Contract, order: Order):
        req = self.register_requests(REQUEST_TYPES.UPDATE_ORDER, orderId, timeout=5, delayWhenFinished=0.1)
        super().placeOrder(orderId, contract, order)
        return req
    
    # dont use warpper, because sub-function used
    def replaceOder(self, orderId: int, contract: Contract, order: Order):
        Logger.log(f"replaceOder. orderId: {orderId}, contract: {contract}, order: {order}")

        req =self.cancelOrder(orderId)
        req.done.delay_wait()  

        if len(req.response.errors) > 0:
            return req
        
        req2 = self.placeOrder(contract, order)
        return req2    


    
    @RequestWarper
    def cancelOrder(self, orderId: int ):
        cancelOrder = OrderCancel()
        cancelOrder.manualOrderCancelTime = ""
        req =self.register_requests(REQUEST_TYPES.CANCEL_ORDER, orderId, timeout=10, delayWhenFinished=0.1)
        super().cancelOrder(orderId, cancelOrder)
        return req

    @RequestWarper
    def reqAllOpenOrders(self):
        req = self.register_requests(REQUEST_TYPES.GET_OPEN_ORDERS, timeout=10, delayWhenFinished=0.1)
        super().reqAllOpenOrders()
        return req


        
    def error(self, tickerId: TickerId, errorCode: int, errorString: str , contract: Any = None):

        Logger.log(f"error. reqId: {tickerId}, errorCode: {errorCode}, errorString: {errorString} contract:{contract}" )

        msg = {"errorCode": errorCode, "errorString": errorString}

        req = None
        if tickerId != -1:
            req = self.find_request_by_reqId(tickerId) 
    
        elif errorCode in WARNING_CANCEL_UPDATEACCOUNT.MESSAGES:
            req = self.find_request(REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT) 
            if req is not None:
                req.keepAlive = False

        elif errorCode in ERROR_CONNECTION.MESSAGES:
            req = self.find_request(REQUEST_TYPES.CONNECT) 
            self.TWS_connection = False        
            self.accountUpdateTime = ""


     
        if req is not None:
            if errorCode in MESSAGE_INPUT_ERROR.MESSAGES:
                req.keepAlive = False

            if errorCode in WARNING.MESSAGES:
                req.response.warnings.append(msg)

            # Cancel in TWS  will not be assign message 
            if errorCode in MESSAGE_DONE.MESSAGES:
                req.response.infos.append(msg)
            else:
                req.response.errors.append(msg)

            req.done.set()
            return


        if errorCode in INFO_TWS_AVILABLE.SUCCESS_MESSAGES:
            self.TWS_aviliable = True
        
        elif errorCode in INFO_TWS_AVILABLE.FAIL_MESSAGES:
            self.TWS_aviliable = False



   # the reqId is refer to orderId , not request Id, so can't remove request by reqId
    @RequestWarper
    def reqExecutions(self, reqId: int, filter: ExecutionFilter):
        self.execDetailsData = {}
        req = self.register_requests(REQUEST_TYPES.GET_EXECUTIONS, -1, timeout=10)
        super().reqExecutions( reqId, filter)
        return req
    
    #it will also be exec when order is filled
    def execDetails(self, reqId, contract, execution):
        Logger.log(f"execDetails. reqId: {reqId}, contract: {contract}, execution: {execution}")

        if  execution.execId not in self.execDetailsData:
            self.execDetailsData[execution.execId] = {}

        self.execDetailsData[execution.execId]["contract"] = contract
        self.execDetailsData[execution.execId]["execution"] = execution
   
        req = self.find_request(REQUEST_TYPES.GET_EXECUTIONS)
       
        if req is not None:
            req.response.contents.append(self.execDetailsData)
       



    def commissionReport(self, commissionReport: CommissionReport):
        Logger.log(f"commissionReport. commissionReport: {commissionReport}")
        
        if commissionReport.execId not in self.execDetailsData:
            self.execDetailsData[commissionReport.execId] = {}
            
        self.execDetailsData[commissionReport.execId]["commissionReport"] = commissionReport

        req = self.find_request(REQUEST_TYPES.GET_EXECUTIONS)
        if req is not None:
            req.response.contents.append(self.execDetailsData)
  

  
    def execDetailsEnd(self, reqId: int):
        Logger.log(f"execDetailsEnd. reqId: {reqId}")

        req = self.find_request(REQUEST_TYPES.GET_EXECUTIONS)
        if req is not None:
            req.done.set()
        else:
            raise Exception("execDetailsEnd without request")
        
    


    @RequestWarper
    def reqHistoricalData(self, reqId : int, contract: Contract, endDateTime: str, durationStr: str, barSizeSetting: str, whatToShow: str, useRTH: int, formatDate: int, keepUpToDate: bool, chartOptions: ListOfTagValue):
        timeout = 2 if keepUpToDate  else 0
        req = self.register_requests(REQUEST_TYPES.GET_HISTORICAL_DATA, reqId, keepAlive=keepUpToDate, timeout=timeout)
        super().reqHistoricalData(req.reqId, contract, endDateTime, durationStr, barSizeSetting, whatToShow, useRTH, formatDate, keepUpToDate, chartOptions)
        return req


    def historicalData(self, reqId: int, bar: BarData):
        Logger.log(f"historicalData. reqId: {reqId}, bar: {bar}")
        req = self.find_request_by_reqId(reqId)
        req.response.contents.append(bar) # type: ignore


    def historicalDataUpdate(self, reqId: int, bar: BarData):
        Logger.log(f"historicalDataUpdate. reqId: {reqId}, bar: {bar}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA],reqId)
        req.response.contents.append(bar) # type: ignore
        

    def historicalDataEnd(self, reqId: int, start: str, end: str):
        Logger.log(f"historicalDataEnd. reqId: {reqId}, start: {start}, end: {end}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA],reqId)
        
        if req is not None:
            req.done.set()
        else:
            raise Exception("historicalDataEnd without request")


    def cancelHistoricalData(self, reqId):
        debugpy.breakpoint()
        self.unregister_requests_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_DATA,  REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
      
        req = self.register_requests(REQUEST_TYPES.CANCEL_HISTORICAL_DATA, reqId, timeout=2)
        super().cancelHistoricalData(reqId)
        return req


    @RequestWarper
    def reqRealTimeBars(self, reqId, contract, barSize, whatToShow, useRTH, realTimeBarsOptions):
        req = self.register_requests( REQUEST_TYPES.GET_REAL_TIME_BARS, reqId, keepAlive=True, timeout=2)
        super().reqRealTimeBars(reqId, contract, barSize, whatToShow, useRTH, realTimeBarsOptions)
        return req


    def realTimeBar(self, reqId: int, time: int, open_: float, high: float, low: float, close: float, volume: int, wap: float, count: int):
        Logger.log(f"realTimeBar. reqId: {reqId}, time: {time}, open_: {open_}, high: {high}, low: {low}, close: {close}, volume: {volume}, wap: {wap}, count: {count}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)
        req.response.contents.append({"time": time, "open": open_, "high": high, "low": low, "close": close, "volume": volume, "wap": wap, "count": count}) # type: ignore

    def realtimeBarEnd(self, reqId: int):
        Logger.log(f"realtimeBarEnd. reqId: {reqId}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_REAL_TIME_BARS],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("realtimeBarEnd without request") 

    @RequestWarper
    def cancelRealTimeBars(self, reqId):
        self.unregister_requests(REQUEST_TYPES.GET_REAL_TIME_BARS, reqId)
        req = self.register_requests( REQUEST_TYPES.CANCEL_REAL_TIME_BARS, reqId, timeout=2)
        super().cancelRealTimeBars(reqId)
        return req



    @RequestWarper
    def reqMktData(self, reqId: int, contract: Contract, genericTickList: str, snapshot: bool, regulatorySnapshot: bool, mktDataOptions: ListOfTagValue):
        req = self.register_requests(REQUEST_TYPES.GET_MARKET_DATA, reqId, keepAlive=True , timeout=2)
        super().reqMktData( reqId, contract, genericTickList, snapshot, regulatorySnapshot, mktDataOptions)
        return req

    def tickPrice(self, reqId: int, tickType: int, price: float, attrib: TickAttrib):
        Logger.log(f"tickPrice. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, price: {price}, attrib: {attrib}")

    
    def tickSize(self, reqId: int, tickType: int, size: int):
        Logger.log(f"tickSize. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, size: {size}")



    def tickString(self, reqId: int, tickType: int, value: str):
        Logger.log(f"tickString. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, value: {value}")

    
    def tickGeneric(self, reqId: int, tickType: int, value: float):
        Logger.log(f"tickGeneric. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, value: {value}")


    def tickEFP(self, reqId: int, tickType: int, basisPoints: float, formattedBasisPoints: str,
                impliedFuture: float, holdDays: int, futureExpiry: str, dividendImpact: float,
                dividendsToExpiry: float):
        Logger.log(f"tickEFP. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, basisPoints: {basisPoints}, formattedBasisPoints: {formattedBasisPoints}, impliedFuture: {impliedFuture}, holdDays: {holdDays}, futureExpiry: {futureExpiry}, dividendImpact: {dividendImpact}, dividendsToExpiry: {dividendsToExpiry}")

    def tickSnapshotEnd(self, reqId: int):
        Logger.log(f"tickSnapshotEnd. reqId: {reqId}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DATA],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("tickSnapshotEnd without request") 


    def tickReqParams(self, reqId, tickType, minTick, bboExchange, snapshotPermissions):
        Logger.log(f"tickReqParams. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, minTick: {minTick}, bboExchange: {bboExchange}, snapshotPermissions: {snapshotPermissions}")


    def tickOptionComputation(self, reqId, tickType, tickAttrib, impliedVol, delta, optPrice, pvDividend, gamma, vega, theta, undPrice):
        Logger.log(f"tickOptionComputation. reqId: {reqId}, tickType: {TickTypeEnum.toStr(tickType)}, tickAttrib: {tickAttrib}, impliedVol: {impliedVol}, delta: {delta}, optPrice: {optPrice}, pvDividend: {pvDividend}, gamma: {gamma}, vega: {vega}, theta: {theta}, undPrice: {undPrice}")
    
    
    @RequestWarper
    def reqMarketDataType(self, marketDataType):
        req = self.register_requests(REQUEST_TYPES.GET_MARKET_DATA_TYPE, marketDataType, timeout=2)
        super().reqMarketDataType(marketDataType)
        return req


    #it will call back after reqMktData
    def marketDataType(self, reqId: int, marketDataType: int):
        Logger.log(f"marketDataType. reqId: {reqId}, marketDataType: {MarketDataTypeEnum.toStr(marketDataType)}")




    @RequestWarper
    def cancelMktData(self, reqId):
        self.unregister_requests(REQUEST_TYPES.GET_MARKET_DATA, reqId)

        req = self.register_requests(REQUEST_TYPES.CANCEL_MARKET_DATA, reqId, timeout=2)
        super().cancelMktData(reqId)
        return req


    @RequestWarper
    def reqHistoricalTicks(self, reqId : int, contract  : Contract, startDateTime : str, endDateTime : str, nTicks : int, whatToShow : str, useRTH : int, ignoreSize  : bool, miscOptions : ListOfTagValue):
        req = self.register_requests(REQUEST_TYPES.GET_HISTORICAL_TICKS, reqId, delayWhenFinished=0.1)
        super().reqHistoricalTicks(reqId, contract, startDateTime, endDateTime, nTicks, whatToShow, useRTH, ignoreSize, miscOptions)
        return req
    

    def historicalTicks(self, reqId: int, ticks: ListOfHistoricalTick, done: bool):
        Logger.log(f"historicalTicks. reqId: {reqId}, ticks: {ticks}, done: {done}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
        req.response.contents.append(ticks) # type: ignore

    def historicalTicksLast(self, reqId: int, ticks: ListOfHistoricalTickLast, done: bool):
        Logger.log(f"historicalTicksLast. reqId: {reqId}, ticks: {ticks}, done: {done}")

        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_HISTORICAL_TICKS],reqId)
        req.response.contents.append(ticks) # type: ignore

        if req is not None:
            req.done.set()
        else:
            raise Exception("historicalTicksLast without request") 



    @RequestWarper
    def reqTickByTickData(self, reqId, contract, tickType, numberOfTicks, ignoreSize):
        req = self.register_requests(REQUEST_TYPES.GET_TICK_BY_TICK_DATA, reqId, keepAlive=True)
        super().reqTickByTickData(reqId, contract, tickType, numberOfTicks, ignoreSize)
        return req


    def tickByTickAllLast(self, reqId: int, tickType: int, time: int, price: float,
                          size: int, tickAttribLast: TickAttribLast,
                          exchange: str, specialConditions: str):
        Logger.log(f"tickByTickAllLast. reqId: {reqId}, tickType: {tickType}, time: {time}, price: {price}, size: {size}, tickAttribLast: {tickAttribLast}, exchange: {exchange}, specialConditions: {specialConditions}")  
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_TICK_BY_TICK_DATA],reqId)
        req.response.contents.append({"tickType": tickType, "time": time, "price": price, "size": size, "tickAttribLast": tickAttribLast, "exchange": exchange, "specialConditions": specialConditions}) # type: ignore

    
    @RequestWarper
    def cancelTickByTickData(self, reqId):
        self.unregister_requests(REQUEST_TYPES.GET_TICK_BY_TICK_DATA, reqId)
        req = self.register_requests(REQUEST_TYPES.CANCEL_TICK_BY_TICK_DATA, reqId, timeout=2)
        super().cancelTickByTickData(reqId)
        return req


    @RequestWarper
    def reqMktDepth(self, reqId, contract, numRows, isSmartDepth, mktDepthOptions=[]):
        req = self.register_requests(REQUEST_TYPES.GET_MARKET_DEPTH, reqId, keepAlive=True)
        super().reqMktDepth(reqId, contract, numRows, isSmartDepth, mktDepthOptions)
        return req
    
    def updateMktDepth(self, reqId: int, position: int, operation: int,
                        side: int, price: float, size: int):
        Logger.log(f"updateMktDepth. reqId: {reqId}, position: {position}, operation: {operation}, side: {side}, price: {price}, size: {size}")
        req = self.find_request_by_type_list( [REQUEST_TYPES.GET_MARKET_DEPTH],reqId)
        req.response.contents.append({"position": position, "operation": operation, "side": side, "price": price, "size": size}) # type: ignore


    @RequestWarper
    def cancelMktDepth(self, reqId, isSmartDepth=False):
        self.unregister_requests(REQUEST_TYPES.GET_MARKET_DEPTH, reqId)
        req = self.register_requests(REQUEST_TYPES.CANCEL_MARKET_DEPTH, reqId, timeout=2)
        super().cancelMktDepth(reqId, isSmartDepth)
        return req
    



    @RequestWarper
    def reqAccountUpdates(self, accountCode: str):
        if self.accountUpdateTime != "":
            req = self.cancelAccountUpdates(accountCode)
            req.done.delay_wait()


        self.accountData = {}
        self.protfolioData = {}
        self.accountUpdateTime = ""

        req2 = self.register_requests(REQUEST_TYPES.UPDATE_ACCOUNT, keepAlive=True )
        super().reqAccountUpdates(True, accountCode)
        return req2

    
    @RequestWarper
    def cancelAccountUpdates(self, accountCode: str):
        self.unregister_requests(REQUEST_TYPES.UPDATE_ACCOUNT, -1)

        req2 = self.register_requests(REQUEST_TYPES.CANCEL_UPDATE_ACCOUNT, timeout=2 , noResponse=True)
        super().reqAccountUpdates(False,accountCode)
        
        return req2

    
    def updatePortfolio(self, contract: Contract, position: float,
                        marketPrice: float, marketValue: float,
                        averageCost: float, unrealizedPNL: float,
                        realizedPNL: float, accountName: str):

        Logger.log(f"updatePortfolio. contract: {contract}, position: {position}, marketPrice: {marketPrice}, marketValue: {marketValue}, averageCost: {averageCost}, unrealizedPNL: {unrealizedPNL}, realizedPNL: {realizedPNL}, accountName: {accountName}")
        
        if accountName not in self.protfolioData:
            self.protfolioData[accountName] = {}
        
        self.protfolioData[accountName][contract.conId] = {'contract': contract, 'position': position, 'marketPrice': marketPrice, 'marketValue': marketValue, 'averageCost': averageCost, 'unrealizedPNL': unrealizedPNL, 'realizedPNL': realizedPNL, 'accountName': accountName}
         
        

    def updateAccountValue(self, key: str, val: str, currency: str,
                          accountName: str):
        
        Logger.log(f"updateAccountValue. key: {key}, val: {val}, currency: {currency}, accountName: {accountName}")

        if accountName not in self.accountData:
            self.accountData[accountName] = {}
        
        self.accountData[accountName][key] = {'val': val, 'currency': currency, 'accountName': accountName}
       

   

    def updateAccountTime(self, timeStamp: str):
        Logger.log(f"updateAccountTime. timeStamp: {timeStamp}")
        self.accountUpdateTime = timeStamp

 


    def accountDownloadEnd(self, accountName: str):
        Logger.log(f"accountDownloadEnd. accountName: {accountName}")
       
        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT])
        if req is not None:
            req.response.contents.append({"accountData": self.accountData, "protfolioData": self.protfolioData, "accountUpdateTime": self.accountUpdateTime})
            req.done.set()
        else:
            raise Exception("accountDownloadEnd without request")
        

    @RequestWarper
    def reqAccountSummary(self, reqId: int, groupName: str, tags: str):
        req = self.find_request(REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY, reqId) 
        if req is not None:
            req2 = self.cancelAccountSummary(req.reqId)
            req2.done.delay_wait()

        self.accountSummaryData = {}

        req3 = self.register_requests(REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY, reqId, timeout=10, keepAlive=True)
        super().reqAccountSummary(reqId, groupName, tags)
        return req3


    def accountSummary(self, reqId: int, account: str, tag: str, value: str,
                      currency: str):
        Logger.log(f"accountSummary. reqId: {reqId}, account: {account}, tag: {tag}, value: {value}, currency: {currency}")

        if account not in self.accountSummaryData:
            self.accountSummaryData[account] = {}
        
        self.accountSummaryData[account][tag] = {'value': value, 'currency': currency}

        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY],reqId)
        if req is not None:
            req.response.contents.append(self.accountSummaryData)
        else:
            raise Exception("accountSummary without request")



    def accountSummaryEnd(self, reqId: int):
        Logger.log(f"accountSummaryEnd. reqId: {reqId}")

        req = self.find_request_by_type_list([REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("accountSummaryEnd without request")


    @RequestWarper
    def cancelAccountSummary(self, reqId: int):
        self.unregister_requests(REQUEST_TYPES.UPDATE_ACCOUNT_SUMMARY, reqId)
       
        req2 = self.register_requests(REQUEST_TYPES.CANCEL_ACCOUNT_SUMMARY, reqId, timeout=2 , noResponse=True)
        super().cancelAccountSummary(reqId)
        return req2





    @RequestWarper
    def reqPositions(self):
        req = self.register_requests(REQUEST_TYPES.GET_POSITIONS, keepAlive=True)
        super().reqPositions()
        return req
    

    def position(self, account: str, contract: Contract, pos: float,
                avgCost: float):
        Logger.log(f"position. account: {account}, contract: {contract}, pos: {pos}, avgCost: {avgCost}")

        # if account not in self.positionData:
        #     self.positionData[account] = {}
                
        # self.positionData[account][contract.conId] = {'contract': contract, 'pos': pos, 'avgCost': avgCost}

        # req = self.find_request_by_type_list([REQUEST_TYPES.GET_POSITIONS])
        # if req is not None:
        #     req.response.contents.append(self.positionData)
        # else:
        #     raise Exception("position without request")

        
    def positionEnd(self):
        Logger.log(f"positionEnd")
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_POSITIONS])
        if req is not None:
            req.done.set()
        else:
            raise Exception("positionEnd without request")


    @RequestWarper
    def cancelPositions(self):
        self.unregister_requests(REQUEST_TYPES.GET_POSITIONS, -1)
        req = self.register_requests(REQUEST_TYPES.CANCEL_POSITIONS, timeout=2)
        super().cancelPositions()
        return req


    def nextValidId(self, orderId: int):
        Logger.log(f"nextValidId. orderId: {orderId}")
        if orderId <= TickerID_RULES.REQUEST_ID_MAX:
            self.nextOrderId = TickerID_RULES.REQUEST_ID_MAX + 1
        else:
            self.nextOrderId = orderId
      
        req = self.find_request_by_type_list([REQUEST_TYPES.PLACE_ORDER],orderId)
        if req is not None:
            req.done.set()

        req2 = self.find_request_by_type_list([REQUEST_TYPES.CONNECT, REQUEST_TYPES.GET_IDS])
        if req2 is not None:
            req2.done.set()

        
    

    @RequestWarper
    def reqIds(self):
        req = self.register_requests(REQUEST_TYPES.GET_IDS, timeout=5)
        super().reqIds(0)
        return req


    @RequestWarper
    def reqCompletedOrders(self, apiOnly):
        req = self.register_requests(REQUEST_TYPES.GET_COMPLETED_ORDERS)
        super().reqCompletedOrders(apiOnly)
        return req

    def completedOrders(self, contract: Contract, order: Order, orderState: OrderState):
        Logger.log(f"completedOrders. contract: {contract}, order: {order}, orderState: {orderState}")

        self.completedOrdersData[order.orderId] =  {"contract": contract, "order": order, "orderState": orderState}

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_COMPLETED_ORDERS])
        if req is not None:
            req.response.contents.append(self.completedOrdersData)
      
        else:
            raise Exception("completedOrders without request")


    def completedOrdersEnd(self):
        Logger.log(f"completedOrdersEnd")
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_COMPLETED_ORDERS])
        if req is not None:
            req.done.set()
        else:
            raise Exception("completedOrdersEnd without request")


    @RequestWarper
    def reqcurrentTime(self):
        req = self.register_requests(REQUEST_TYPES.GET_CURRENT_TIME, timeout=5)
        super().reqCurrentTime()
        return req


    def currentTime(self, time: int):
        Logger.log(f"currentTime. time: {time}")
        self.sys_currentTime = time

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CURRENT_TIME])
        if req is not None:
            req.done.set()
        else:
            raise Exception("currentTime without request")
        

    @RequestWarper
    def reqFundamentalData(self, reqId, contract, reportType, fundamentalDataOptions):
        req = self.register_requests(REQUEST_TYPES.GET_FUNDAMENTAL_DATA, reqId, timeout=10)
        super().reqFundamentalData(reqId, contract, reportType, fundamentalDataOptions)
        return req
    
    def fundamentalData(self, reqId: int, data: str):
        Logger.log(f"fundamentalData. reqId: {reqId}, data: {data}")

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_FUNDAMENTAL_DATA],reqId)
        if req is not None:
            req.response.contents.append(data)
        else:
            raise Exception("fundamentalData without request")
        

    @RequestWarper
    def cancelFundamentalData(self, reqId: int):
        self.unregister_requests(REQUEST_TYPES.GET_FUNDAMENTAL_DATA, reqId)
        req = self.register_requests(REQUEST_TYPES.CANCEL_FUNDAMENTAL_DATA, reqId, timeout=10)
        super().cancelFundamentalData(reqId)
        return req
    


    @RequestWarper
    def reqContractDetails(self, reqId: int, contract: Contract):
        req = self.register_requests(REQUEST_TYPES.GET_CONTRACT_DETAILS, reqId, timeout=5)
        super().reqContractDetails(reqId, contract)
        return req

    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        

        Logger.log(f"contractDetails. reqId: {reqId}, contractDetails: {contractDetails}")

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CONTRACT_DETAILS],reqId)
        print("contractDetails got req-object id:", id(req), "   contractDetails=", contractDetails)
        if req is not None:
            req.response.contents.append(contractDetails)
        else:
            raise Exception("contractDetails without request")
        
    def bondContractDetails(self, reqId: int, contractDetails: ContractDetails):
        Logger.log(f"bondContractDetails. reqId: {reqId}, contractDetails: {contractDetails}")
        
        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CONTRACT_DETAILS],reqId)
        if req is not None:
            req.response.contents.append(contractDetails)
        else:
            raise Exception("bondContractDetails without request")

    def contractDetailsEnd(self, reqId: int):
        Logger.log(f"contractDetailsEnd. reqId: {reqId}")

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_CONTRACT_DETAILS],reqId)
        if req is not None:
            req.done.set()
        else:
            raise Exception("contractDetailsEnd without request")
        

    @RequestWarper
    def reqWshEventData(self, reqId: int, wshEventData: WshEventData, MIN_SERVER_VER_WSH_EVENT_DATA_FILTERS_DATE: int):
        req = self.register_requests(REQUEST_TYPES.GET_WSH_EVENT_DATA, reqId, timeout=5)
        super().reqWshEventData(reqId, wshEventData, MIN_SERVER_VER_WSH_EVENT_DATA_FILTERS_DATE)
        return req
    
    def wshEventData(self, reqId: int, wshEventData: WshEventData):
        Logger.log(f"wshEventData. reqId: {reqId}, wshEventData: {wshEventData}")

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_WSH_EVENT_DATA],reqId)
        if req is not None:
            req.response.contents.append(wshEventData)
        else:
            raise Exception("wshEventData without request")
        

    @RequestWarper
    def cancelWshEventData(self, reqId: int):
        self.unregister_requests(REQUEST_TYPES.GET_WSH_EVENT_DATA, reqId)
        req = self.register_requests(REQUEST_TYPES.CANCEL_WSH_EVENT_DATA, reqId, timeout=5)
        super().cancelWshEventData(reqId)
        return req



    @RequestWarper
    def reqWshMetaData(self, reqId: int):
        req = self.register_requests(REQUEST_TYPES.GET_WSH_META_DATA, reqId, timeout=5)
        super().reqWshMetaData(reqId)
        return req
    
    def wshMetaData(self, reqId: int, wshMetaData: str):
        Logger.log(f"wshMetaData. reqId: {reqId}, wshMetaData: {wshMetaData}")

        req = self.find_request_by_type_list([REQUEST_TYPES.GET_WSH_META_DATA],reqId)
        if req is not None:
            req.response.contents.append(wshMetaData)
        else:
            raise Exception("wshMetaData without request")
        

    @RequestWarper
    def cancelWshMetaData(self, reqId: int):
        self.unregister_requests(REQUEST_TYPES.GET_WSH_META_DATA, reqId)
        req = self.register_requests(REQUEST_TYPES.CANCEL_WSH_META_DATA, reqId, timeout=5)
        super().cancelWshMetaData(reqId)
        return req


   
    def connect(self, host, port, clientId):
        req = self.register_requests(REQUEST_TYPES.CONNECT, timeout=10 ,delayWhenFinished=0.1)
        super().connect(host, port, clientId)
        return req


    def reconnect(self):
        attempt = 0
        isConnect  = False
        self.TWS_connection = True
        while isConnect == False and attempt < 10:
            Logger.log("Attempt Connecting...")

            super().disconnect()
            time.sleep(1)

            req = self.connect(self.config['host'], self.config['port'], self.config['clientId'])

           
            thread_run= Thread(target=super().run,args=(), daemon=True)
            thread_run.start()            

            req.done.delay_wait()
     
            isConnect = super().isConnected()
            self.TWS_connection = isConnect


            Logger.log(f"Connection attempt {attempt} : {['Not Success', 'Success'][bool(isConnect)]}")
