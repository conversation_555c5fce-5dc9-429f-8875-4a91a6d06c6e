"""
Notebook helper for capturing callback prints in trading applications
Specifically designed for your TradeTool and IBTool callback scenarios
"""

import sys
import io
import time
import threading
from contextlib import contextmanager
from IPython.display import display, clear_output
import ipywidgets as widgets


class NotebookCallbackMonitor:
    """Monitor and display callback prints in Jupyter notebooks"""
    
    def __init__(self):
        self.output_widget = widgets.Output()
        self.is_monitoring = False
        self.captured_prints = []
        self.original_stdout = sys.stdout
        self.lock = threading.Lock()
        
    def start_monitoring(self, display_widget=True):
        """Start monitoring callback prints"""
        self.is_monitoring = True
        self.captured_prints = []
        
        if display_widget:
            display(self.output_widget)
        
        # Replace stdout with our custom handler
        sys.stdout = self._create_monitoring_stdout()
        
        with self.output_widget:
            print("🔍 Callback monitoring started...")
    
    def stop_monitoring(self):
        """Stop monitoring and restore original stdout"""
        if self.is_monitoring:
            sys.stdout = self.original_stdout
            self.is_monitoring = False
            
            with self.output_widget:
                print(f"✅ Monitoring stopped. Captured {len(self.captured_prints)} callback prints.")
    
    def _create_monitoring_stdout(self):
        """Create a custom stdout that captures and displays prints"""
        class MonitoringStdout:
            def __init__(self, monitor):
                self.monitor = monitor
                
            def write(self, text):
                # Write to original stdout
                self.monitor.original_stdout.write(text)
                
                # Also capture for callback display
                if text.strip() and self.monitor.is_monitoring:
                    with self.monitor.lock:
                        timestamp = time.strftime('%H:%M:%S')
                        self.monitor.captured_prints.append(f"[{timestamp}] {text}")
                        
                        # Display in widget
                        with self.monitor.output_widget:
                            print(f"[{timestamp}] {text}", end='')
                            
            def flush(self):
                self.monitor.original_stdout.flush()
                
        return MonitoringStdout(self)
    
    def clear_output(self):
        """Clear the output widget"""
        with self.output_widget:
            clear_output()
    
    @contextmanager
    def monitor_context(self, duration_seconds=None):
        """Context manager for temporary monitoring"""
        self.start_monitoring()
        try:
            yield self
            if duration_seconds:
                time.sleep(duration_seconds)
        finally:
            self.stop_monitoring()


class TradingCallbackCapture:
    """Specialized capture for trading callback functions"""
    
    def __init__(self):
        self.callback_outputs = {}
        self.monitor = NotebookCallbackMonitor()
        
    def wrap_delay_wait_callback(self, request_obj, callback_name="DelayEvent"):
        """Wrap the delay_wait method to capture its callback prints"""
        if hasattr(request_obj, 'done') and hasattr(request_obj.done, 'delay_wait'):
            original_delay_wait = request_obj.done.delay_wait
            
            def wrapped_delay_wait():
                print(f"🔄 Starting {callback_name} delay_wait...")
                
                # Start capturing
                capture_buffer = io.StringIO()
                old_stdout = sys.stdout
                sys.stdout = capture_buffer
                
                try:
                    result = original_delay_wait()
                    
                    # Get captured output
                    captured = capture_buffer.getvalue()
                    sys.stdout = old_stdout
                    
                    # Display captured output
                    if captured:
                        print(f"📝 Callback output from {callback_name}:")
                        print(captured, end='')
                    else:
                        print(f"✅ {callback_name} completed (no callback output)")
                        
                    return result
                    
                except Exception as e:
                    sys.stdout = old_stdout
                    print(f"❌ Error in {callback_name}: {e}")
                    raise
                    
            request_obj.done.delay_wait = wrapped_delay_wait
            return request_obj
        else:
            print(f"⚠️ Object doesn't have delay_wait method to wrap")
            return request_obj
    
    def monitor_contract_details_request(self, tt_instance, contract, req_id=9):
        """Monitor a contract details request with full callback capture"""
        print(f"🔍 Monitoring contract details request for {contract.symbol}")
        
        # Start monitoring
        self.monitor.start_monitoring()
        
        try:
            # Make the request
            req = tt_instance.ib.reqContractDetails(req_id, contract)
            print(f"📤 Request sent for {contract.symbol}")
            
            # Wrap the delay_wait to capture callback prints
            req = self.wrap_delay_wait_callback(req, f"ContractDetails-{contract.symbol}")
            
            # Wait for completion
            req.done.delay_wait()
            
            # Display results
            print(f"\n📊 Results for {contract.symbol}:")
            print(f"   Contents: {len(req.response.contents)} items")
            print(f"   Errors: {len(req.response.errors)} errors")
            
            return req
            
        finally:
            self.monitor.stop_monitoring()


# Convenience functions for immediate use in notebooks
def capture_next_callback_prints(duration_seconds=10):
    """Simple function to capture callback prints for the next few seconds"""
    print(f"🔍 Capturing callback prints for {duration_seconds} seconds...")
    
    captured = []
    original_stdout = sys.stdout
    
    class CaptureStdout:
        def write(self, text):
            original_stdout.write(text)  # Still show in notebook
            if text.strip():
                captured.append(f"[{time.strftime('%H:%M:%S')}] {text}")
                
        def flush(self):
            original_stdout.flush()
    
    sys.stdout = CaptureStdout()
    
    def restore_stdout():
        time.sleep(duration_seconds)
        sys.stdout = original_stdout
        
        if captured:
            print(f"\n📋 Captured callback prints:")
            for line in captured:
                print(line, end='')
        else:
            print(f"\n✅ No callback prints captured")
    
    # Start timer in background
    timer_thread = threading.Thread(target=restore_stdout, daemon=True)
    timer_thread.start()
    
    return captured


def setup_trading_callback_monitor():
    """Quick setup for trading callback monitoring"""
    monitor = TradingCallbackCapture()
    
    print("🔧 Trading callback monitor setup complete!")
    print("Usage examples:")
    print("  monitor.monitor_contract_details_request(tt, contract)")
    print("  monitor.wrap_delay_wait_callback(request_obj)")
    
    return monitor


# Example usage for your specific case
def demo_usage():
    """Demo of how to use these utilities"""
    print("=== Callback Print Capture Demo ===")
    
    # Method 1: Simple capture for next few seconds
    print("\n1. Simple capture:")
    captured = capture_next_callback_prints(5)
    
    # Method 2: Full monitoring setup
    print("\n2. Full monitoring setup:")
    monitor = setup_trading_callback_monitor()
    
    # Method 3: Context manager
    print("\n3. Context manager usage:")
    notebook_monitor = NotebookCallbackMonitor()
    
    print("\nNow run your trading code and see the captured output!")


if __name__ == "__main__":
    demo_usage()
