{"cells": [{"cell_type": "code", "execution_count": 2, "id": "36869489", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Worker: Waiting on event with action 'order1'...\n", "Worker2: Waiting on event with action 'order2'...\n", "Main: Setting the event to proceed.\n", "Main2: Setting the event to proceed.Event for action 'order1' unregistered from parent.\n", "Worker: Event processing completed.\n", "\n", "Event for action 'order2' unregistered from parent.\n", "Worker2: Event processing completed.\n", "Remaining requests in parent: 0\n"]}], "source": ["import threading\n", "import time\n", "\n", "class DelayEvent(threading.Event):\n", "    def __init__(self, delay: float = 0.0, delegate_method=None):\n", "        \"\"\"\n", "        :param delay: extra delay in seconds after the event is set\n", "        :param delegate_method: a callback function to perform a cleanup action\n", "                                (e.g., removing this event’s parent from a collection)\n", "        \"\"\"\n", "        super().__init__()\n", "        self.delay = delay\n", "        self.delegate_method = delegate_method\n", "\n", "    def delay_wait(self):\n", "        \"\"\"Wait for the event, then sleep for an extra `delay` seconds,\n", "        and finally call the delegate method.\"\"\"\n", "        result = self.wait()  # Block until the event is set\n", "        time.sleep(self.delay)\n", "        if self.delegate_method:\n", "            # We call the bound delegate; the delegate will know which parent object to remove.\n", "            self.delegate_method(result)\n", "        return True\n", "\n", "class Request:\n", "    def __init__(self, delay, delegate_method, action: str):\n", "        self.action = action\n", "        self.error = None\n", "        self.delegate_method = delegate_method\n", "        # Instead of passing delegate_method directly, we wrap it in a lambda so that\n", "        # when <PERSON><PERSON><PERSON><PERSON> calls the delegate, we pass self (the Request) into unregister.\n", "        self.done = DelayEvent(delay, delegate_method=self.callbackWhenDoneFinished )\n", "\n", "    def callback<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(self, timeout):\n", "        if timeout:\n", "            self.makeTimeoutContent()\n", "\n", "        self.delegate_method(self)\n", "\n", "    def makeT<PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        self.error = \"timeout\"\n", "        return self\n", "\n", "\n", "class Parent:\n", "    def __init__(self):\n", "        self.requests = []\n", "\n", "    def register(self, action: str, delay: float = 0.1):\n", "        \"\"\"\n", "        Registers an event with an action identifier.\n", "        The event's delegate is set to this parent's unregister method.\n", "        \"\"\"\n", "        req = Request(delay, delegate_method=self.unregister, action=action)\n", "        self.requests.append(req)\n", "        return req\n", "\n", "    def unregister(self, req: Request):\n", "        \"\"\"\n", "        Callback that removes the given request (parent object) from the parent's list.\n", "        \"\"\"\n", "        if req in self.requests:\n", "            self.requests.remove(req)\n", "            print(f\"Event for action '{req.action}' unregistered from parent.\")\n", "        else:\n", "            print(f\"Event for action '{req.action}' not found in parent's requests.\")\n", "\n", "# Example Usage:\n", "parent = Parent()\n", "# Register an event with action \"order1\"\n", "req = parent.register(\"order1\", delay=3)\n", "\n", "def worker(req: Request):\n", "    print(f\"Worker: Waiting on event with action '{req.action}'...\")\n", "    req.done.delay_wait()  # Waits for the event, delays extra, and calls parent's unregister.\n", "    print(\"Worker: Event processing completed.\")\n", "\n", "# Start a worker thread for the event\n", "thread = threading.Thread(target=worker, args=(req,))\n", "thread.start()\n", "\n", "\n", "# Example Usage:\n", "\n", "# Register an event with action \"order1\"\n", "req2 = parent.register(\"order2\", delay=3)\n", "\n", "def worker(req: Request):\n", "    print(f\"Worker2: Waiting on event with action '{req.action}'...\")\n", "    req.done.delay_wait()  # Waits for the event, delays extra, and calls parent's unregister.\n", "    print(\"Worker2: Event processing completed.\")\n", "\n", "# Start a worker thread for the event\n", "thread2 = threading.Thread(target=worker, args=(req2,))\n", "thread2.start()\n", "\n", "\n", "# Simulate some processing delay before setting the event.\n", "time.sleep(1)\n", "print(\"Main: Setting the event to proceed.\")\n", "req.done.set()\n", "\n", "\n", "time.sleep(3)\n", "print(\"Main2: Setting the event to proceed.\", flush=True)\n", "req2.done.set()\n", "\n", "# Optional: Wait for the worker to finish.\n", "thread.join()\n", "thread2.join()\n", "\n", "print(\"Remaining requests in parent:\", len(parent.requests))\n"]}, {"cell_type": "code", "execution_count": null, "id": "d4fe52c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      Unnamed: 0        date      open      high       low     close  \\\n", "0              0  2010-06-29    1.2667    1.6667    1.1693    1.5927   \n", "1              1  2010-06-30    1.6713    2.0280    1.5533    1.5887   \n", "2              2  2010-07-01    1.6627    1.7280    1.3513    1.4640   \n", "3              3  2010-07-02    1.4700    1.5500    1.2473    1.2800   \n", "4              4  2010-07-06    1.2867    1.3333    1.0553    1.0740   \n", "...          ...         ...       ...       ...       ...       ...   \n", "2976        2976  2023-04-10  183.5600  185.9000  176.1100  184.4000   \n", "2977        2977  2023-04-11  184.5100  189.1900  184.1500  186.6000   \n", "2978        2978  2023-04-12  186.2900  191.5900  179.7500  179.9000   \n", "2979        2979  2023-04-13  181.2500  186.5000  180.3300  185.9500   \n", "2980        2980  2023-04-14  185.3600  186.5700  182.0100  185.0000   \n", "\n", "           volume  \n", "0     277519500.0  \n", "1     253039500.0  \n", "2     121461000.0  \n", "3      75871500.0  \n", "4     101664000.0  \n", "...           ...  \n", "2976  123177931.0  \n", "2977  100721415.0  \n", "2978  131472591.0  \n", "2979   99401779.0  \n", "2980   84119837.0  \n", "\n", "[2981 rows x 7 columns]\n"]}], "source": ["print(df1)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}