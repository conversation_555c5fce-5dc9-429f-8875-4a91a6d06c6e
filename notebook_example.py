"""
Ready-to-use notebook cell code for capturing callback prints
Copy and paste this directly into your Jupyter notebook
"""

# === IMMEDIATE SOLUTION - COPY THIS INTO YOUR NOTEBOOK CELL ===

import sys
import io
import time
import threading

def capture_callback_prints(duration=10):
    """Capture callback prints for specified duration"""
    print(f"🔍 Capturing callback prints for {duration} seconds...")
    
    captured = []
    original_stdout = sys.stdout
    
    class CallbackCapture:
        def write(self, text):
            original_stdout.write(text)  # Still show in notebook
            if text.strip():
                captured.append(f"[{time.strftime('%H:%M:%S')}] {text}")
        def flush(self):
            original_stdout.flush()
    
    sys.stdout = CallbackCapture()
    
    def restore():
        time.sleep(duration)
        sys.stdout = original_stdout
        if captured:
            print(f"\n📋 CAPTURED CALLBACK PRINTS ({len(captured)} items):")
            print("=" * 60)
            for line in captured:
                print(line, end='')
            print("=" * 60)
    
    threading.Thread(target=restore, daemon=True).start()
    return captured

# === USAGE EXAMPLES ===

# Example 1: Capture prints for 10 seconds then run your code
# capture_callback_prints(10)
# req = tt.ib.reqContractDetails(9, contract)
# req.done.delay_wait()

# Example 2: Wrap delay_wait to capture its output
def wrap_delay_wait(request_obj, name="Request"):
    """Wrap delay_wait to capture callback prints"""
    if hasattr(request_obj.done, 'delay_wait'):
        original = request_obj.done.delay_wait
        
        def wrapped():
            print(f"🔄 Starting {name}...")
            old_stdout = sys.stdout
            captured = io.StringIO()
            sys.stdout = captured
            
            try:
                result = original()
                output = captured.getvalue()
                sys.stdout = old_stdout
                
                if output:
                    print(f"📝 Callback output from {name}:")
                    print(output, end='')
                return result
            except:
                sys.stdout = old_stdout
                raise
        
        request_obj.done.delay_wait = wrapped
    return request_obj

# Example 3: Complete monitoring solution
def monitor_contract_request(tt, symbol="AAPL"):
    """Complete example with monitoring"""
    from ibapi.contract import Contract
    
    # Setup contract
    contract = Contract()
    contract.symbol = symbol
    contract.secType = "STK"
    contract.exchange = "SMART"
    contract.currency = "USD"
    
    print(f"🧪 Testing {symbol} with callback monitoring...")
    
    # Start capturing
    capture_callback_prints(15)
    
    # Make request
    req = tt.ib.reqContractDetails(9, contract)
    req = wrap_delay_wait(req, f"ContractDetails-{symbol}")
    
    # Execute
    req.done.delay_wait()
    
    print(f"📊 Results: {len(req.response.contents)} items, {len(req.response.errors)} errors")
    return req

print("✅ Callback capture utilities loaded!")
print("Usage: capture_callback_prints(10) then run your callback code")
print("Or use: monitor_contract_request(tt, 'AAPL')")
