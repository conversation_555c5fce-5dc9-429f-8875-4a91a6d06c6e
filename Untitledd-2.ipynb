{"cells": [{"cell_type": "code", "execution_count": null, "id": "df6237ba", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">05</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">01:33:06</span> Attempt Connecting<span style=\"color: #808000; text-decoration-color: #808000\">...</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m2025\u001b[0m-\u001b[1;36m05\u001b[0m-\u001b[1;36m29\u001b[0m \u001b[1;92m01:33:06\u001b[0m Attempt Connecting\u001b[33m...\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Market data farm connection is OK:usfarm.nj'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2104\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'Market data farm connection is OK:usfarm.nj'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Market data farm connection is OK:jfarm'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2104\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'Market data farm connection is OK:jfarm'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Market data farm connection is OK:usfarm'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2104\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'Market data farm connection is OK:usfarm'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HMDS data farm connection is OK:euhmds'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2106\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'HMDS data farm connection is OK:euhmds'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HMDS data farm connection is OK:fundfarm'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2106\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'HMDS data farm connection is OK:fundfarm'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2106</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'HMDS data farm connection is OK:ushmds'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2106\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'HMDS data farm connection is OK:ushmds'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2158</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Sec-def data farm connection is OK:secdefil'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2158\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'Sec-def data farm connection is OK:secdefil'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">05</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">01:33:08</span> Connection attempt <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> : Success\n", "</pre>\n"], "text/plain": ["\u001b[1;36m2025\u001b[0m-\u001b[1;36m05\u001b[0m-\u001b[1;36m29\u001b[0m \u001b[1;92m01:33:08\u001b[0m Connection attempt \u001b[1;36m1\u001b[0m : Success\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">05</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">01:33:08</span> x'x'x'x'x'x'x'x'x'x'x'x'x'x'x'xxxxxxxxxxxxxxxxx reqAccountUpdates\n", "</pre>\n"], "text/plain": ["\u001b[1;36m2025\u001b[0m-\u001b[1;36m05\u001b[0m-\u001b[1;36m29\u001b[0m \u001b[1;92m01:33:08\u001b[0m x'x'x'x'x'x'x'x'x'x'x'x'x'x'x'xxxxxxxxxxxxxxxxx reqAccountUpdates\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccountCode, val: DU7492998, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccountCode, val: DU7492998, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccountOrGroup, val: DU7492998, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccountReady, val: true, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccountReady, val: true, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccountType, val: INDIVIDUAL, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccountType, val: INDIVIDUAL, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccruedCash, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">810.8806845</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccruedCash, val: \u001b[1;36m810.8806845\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccruedCash, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccruedCash, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccruedCash, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">103.44</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccruedCash, val: \u001b[1;36m103.44\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccruedCash-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">810.88</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccruedCash-S, val: \u001b[1;36m810.88\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccruedDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccruedDividend, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AccruedDividend-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AccruedDividend-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068685.99</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AvailableFunds, val: \u001b[1;36m1068685.99\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068685.99</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AvailableFunds-S, val: \u001b[1;36m1068685.99\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Billable, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Billable, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Billable-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Billable-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: BuyingPower, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7124573.26</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: BuyingPower, val: \u001b[1;36m7124573.26\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: CashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">760472.6138</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: CashBalance, val: \u001b[1;36m760472.6138\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: CashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">388479.14</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: CashBalance, val: \u001b[1;36m388479.14\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: CashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47453.35</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: CashBalance, val: \u001b[1;36m47453.35\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ColumnPrio-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ColumnPrio-S, val: \u001b[1;36m1\u001b[0m, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: CorporateBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: CorporateBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: CorporateBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: CorporateBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: CorporateBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: CorporateBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Cryptocurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Cryptocurrency, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Cryptocurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Cryptocurrency, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Cryptocurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Cryptocurrency, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Currency, val: BASE, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Currency, val: BASE, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Currency, val: HKD, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Currency, val: HKD, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Currency, val: USD, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Currency, val: USD, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Cush<PERSON>, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.940605</span>, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Cush<PERSON>, val: \u001b[1;36m0.940605\u001b[0m, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: EquityWithLoanValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147875.79</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: EquityWithLoanV<PERSON>ue, val: \u001b[1;36m1147875.79\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: EquityWithLoanValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147875.79</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: EquityWithLoanValue-S, val: \u001b[1;36m1147875.79\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079697.78</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExcessLiquidity, val: \u001b[1;36m1079697.78\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079697.78</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExcessLiquidity-S, val: \u001b[1;36m1079697.78\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExchangeRate, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExchangeRate, val: \u001b[1;36m1.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExchangeRate, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExchangeRate, val: \u001b[1;36m1.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExchangeRate, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.8391404</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExchangeRate, val: \u001b[1;36m7.8391404\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullAvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068685.99</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullAvailableFunds, val: \u001b[1;36m1068685.99\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullAvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068685.99</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullAvailableFunds-S, val: \u001b[1;36m1068685.99\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079697.78</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullExcessLiquidity, val: \u001b[1;36m1079697.78\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079697.78</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullExcessLiquidity-S, val: \u001b[1;36m1079697.78\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullInitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79189.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullInitMarginReq, val: \u001b[1;36m79189.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullInitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79189.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullInitMarginReq-S, val: \u001b[1;36m79189.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullMaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68178.09</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullMaintMarginReq, val: \u001b[1;36m68178.09\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullMaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68178.09</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullMaintMarginReq-S, val: \u001b[1;36m68178.09\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FundValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FundValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FundValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FutureOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FutureOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FutureOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FutureOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FutureOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FutureOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FuturesPNL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FuturesPNL, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FuturesPNL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FuturesPNL, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FuturesPNL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FuturesPNL, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FxCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FxCashBalance, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FxCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FxCashBalance, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FxCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FxCashBalance, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: GrossPositionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">387533.79</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: GrossPositionValue, val: \u001b[1;36m387533.79\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: GrossPositionValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">387533.79</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: GrossPositionValue-S, val: \u001b[1;36m387533.79\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: <PERSON><PERSON><PERSON><PERSON>, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: <PERSON><PERSON><PERSON><PERSON>, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Guarantee-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Guarantee-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IncentiveCoupons, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IncentiveCoupons, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IncentiveCoupons-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IncentiveCoupons-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IndianStockHaircut, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IndianStockHaircut, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IndianStockHaircut-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IndianStockHaircut-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: InitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79189.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: InitMarginReq, val: \u001b[1;36m79189.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: InitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79189.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: InitMarginReq-S, val: \u001b[1;36m79189.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IssuerOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IssuerOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IssuerOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IssuerOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: IssuerOptionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: IssuerOptionValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Leverage-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.34</span>, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: Leverage-S, val: \u001b[1;36m0.34\u001b[0m, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadAvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068685.99</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadAvailableFunds, val: \u001b[1;36m1068685.99\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadAvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068685.99</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadAvailableFunds-S, val: \u001b[1;36m1068685.99\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079697.78</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadExcessLiquidity, val: \u001b[1;36m1079697.78\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079697.78</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadExcessLiquidity-S, val: \u001b[1;36m1079697.78\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadInitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79189.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadInitMarginReq, val: \u001b[1;36m79189.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadInitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79189.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadInitMarginReq-S, val: \u001b[1;36m79189.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadMaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68178.09</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadMaintMarginReq, val: \u001b[1;36m68178.09\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadMaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68178.09</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadMaintMarginReq-S, val: \u001b[1;36m68178.09\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadNextChange, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">**********</span>, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadNextChange, val: \u001b[1;36m**********\u001b[0m, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68178.09</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MaintMarginReq, val: \u001b[1;36m68178.09\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68178.09</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MaintMarginReq-S, val: \u001b[1;36m68178.09\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MoneyMarketFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MoneyMarketFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MoneyMarketFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MoneyMarketFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MoneyMarketFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MoneyMarketFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MutualFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MutualFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MutualFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MutualFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MutualFundValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MutualFundValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NLVAndMarginInReview, val: false, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NLVAndMarginInReview, val: false, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetDividend, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetDividend, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetDividend, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetDividend, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidation, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147875.87</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidation, val: \u001b[1;36m1147875.87\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidation-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147875.87</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidation-S, val: \u001b[1;36m1147875.87\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147875.8743</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m1147875.8743\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">388478.4313</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m388478.4313\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">96872.54</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m96872.54\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationUncertainty, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.08</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationUncertainty, val: \u001b[1;36m0.08\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: OptionMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: OptionMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: OptionMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: OptionMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: OptionMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: OptionMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PASharesValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PASharesValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PASharesValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PASharesValue-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PhysicalCertificateValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PhysicalCertificateValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PhysicalCertificateValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PhysicalCertificateValue-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PostExpirationExcess, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PostExpirationExcess, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PostExpirationExcess-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PostExpirationExcess-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PostExpirationMargin, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PostExpirationMargin, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: PostExpirationMargin-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: PostExpirationMargin-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: RealCurrency, val: BASE, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: RealCurrency, val: BASE, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: RealCurrency, val: HKD, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: RealCurrency, val: HKD, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: RealCurrency, val: USD, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: RealCurrency, val: USD, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: RealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: RealizedPnL, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: RealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: RealizedPnL, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: RealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: RealizedPnL, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: SegmentTitle-S, val: Securities, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: SegmentTitle-S, val: Securities, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">386593.09</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: StockMarketValue, val: \u001b[1;36m386593.09\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: StockMarketValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">49315.75</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: StockMarketValue, val: \u001b[1;36m49315.75\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TBillValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TBillValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TBillValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TBillValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TBillValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TBillValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TBondValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TBondValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">760472.6138</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalCashBalance, val: \u001b[1;36m760472.6138\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">388479.14</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalCashBalance, val: \u001b[1;36m388479.14\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalCashBalance, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47453.35</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalCashBalance, val: \u001b[1;36m47453.35\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalCashValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">760472.61</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalCashValue, val: \u001b[1;36m760472.61\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalCashValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">760472.61</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalCashValue-S, val: \u001b[1;36m760472.61\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalDebitCardPendingCharges, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalDebitCardPendingCharges, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TotalDebitCardPendingCharges-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TotalDebitCardPendingCharges-S, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: TradingType-S, val: STKNOPT, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: TradingType-S, val: STKNOPT, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">109764.50</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m109764.50\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-0.71</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m-0.71\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14002.20</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m14002.20\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: WarrantValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: WarrantValue, val: \u001b[1;36m0.00\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: WarrantValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: WarrantValue, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: WarrantValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: WarrantValue, val: \u001b[1;36m0.00\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4815747</span>,NVDA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,NVDA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">136.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">272.83</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">105.38505</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">62.06</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m4815747\u001b[0m,NVDA,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,NVDA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \n", "\u001b[1;36m136.********\u001b[0m, marketValue: \u001b[1;36m272.83\u001b[0m, averageCost: \u001b[1;36m105.38505\u001b[0m, unrealizedPNL: \u001b[1;36m62.06\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,PLTR,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,PLTR,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">124.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">124.45</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.8394</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">33.61</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,PLTR,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,PLTR,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m1\u001b[0m, \n", "marketPrice: \u001b[1;36m124.********\u001b[0m, marketValue: \u001b[1;36m124.45\u001b[0m, averageCost: \u001b[1;36m90.8394\u001b[0m, unrealizedPNL: \u001b[1;36m33.61\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,QQQ,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,QQQ,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">60</span>, marketPrice:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">521.4359741</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31286.16</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">371.94</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8969.76</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,QQQ,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,QQQ,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m60\u001b[0m, marketPrice:\n", "\u001b[1;36m521.4359741\u001b[0m, marketValue: \u001b[1;36m31286.16\u001b[0m, averageCost: \u001b[1;36m371.94\u001b[0m, unrealizedPNL: \u001b[1;36m8969.76\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,TEM,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TEM,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">489.96</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39.0671778</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">138.36</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,TEM,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TEM,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m9\u001b[0m, marketPrice: \n", "\u001b[1;36m54.********\u001b[0m, marketValue: \u001b[1;36m489.96\u001b[0m, averageCost: \u001b[1;36m39.0671778\u001b[0m, unrealizedPNL: \u001b[1;36m138.36\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TLH,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,ARCA,USD,TLH,TLH,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">98.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">197.27</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">102.49</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-7.71</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,TLH,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,ARCA,USD,TLH,TLH,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \n", "\u001b[1;36m98.********\u001b[0m, marketValue: \u001b[1;36m197.27\u001b[0m, averageCost: \u001b[1;36m102.49\u001b[0m, unrealizedPNL: \u001b[1;36m-7.71\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TLT,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TLT,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">85.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">170.69</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">95.60305</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-20.51</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,TLT,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TLT,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \n", "\u001b[1;36m85.********\u001b[0m, marketValue: \u001b[1;36m170.69\u001b[0m, averageCost: \u001b[1;36m95.60305\u001b[0m, unrealizedPNL: \u001b[1;36m-20.51\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">46</span>, \n", "marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">362.2973633</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16665.68</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">257.********</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4833.12</span>, realizedPNL: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m46\u001b[0m, \n", "marketPrice: \u001b[1;36m362.2973633\u001b[0m, marketValue: \u001b[1;36m16665.68\u001b[0m, averageCost: \u001b[1;36m257.********\u001b[0m, unrealizedPNL: \u001b[1;36m4833.12\u001b[0m, realizedPNL: \n", "\u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,USD,CFD,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,,HKD,USD.HKD,USD.HKD,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">120</span>, \n", "marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">940.7</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.84504</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-0.71</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,USD,CFD,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,,HKD,USD.HKD,USD.HKD,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m120\u001b[0m, \n", "marketPrice: \u001b[1;36m7.********\u001b[0m, marketValue: \u001b[1;36m940.7\u001b[0m, averageCost: \u001b[1;36m7.84504\u001b[0m, unrealizedPNL: \u001b[1;36m-0.71\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:02</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:02\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,VGLT,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,VGLT,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54.3540001</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">108.71</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57.59525</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-6.48</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,VGLT,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,VGLT,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice:\n", "\u001b[1;36m54.3540001\u001b[0m, marketValue: \u001b[1;36m108.71\u001b[0m, averageCost: \u001b[1;36m57.59525\u001b[0m, unrealizedPNL: \u001b[1;36m-6.48\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:32</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:32\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">finish\n", "</pre>\n"], "text/plain": ["finish\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Market data farm connection is OK:cafarm'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2104\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'Market data farm connection is OK:cafarm'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'reqId'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorCode'</span>: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2104</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'errorString'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Market data farm connection is OK:cashfarm'</span><span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'reqId'\u001b[0m: \u001b[1;36m0\u001b[0m, \u001b[32m'errorCode'\u001b[0m: \u001b[1;36m2104\u001b[0m, \u001b[32m'errorString'\u001b[0m: \u001b[32m'Market data farm connection is OK:cashfarm'\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: Cush<PERSON>, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.940599</span>, currency: , accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: <PERSON>ush<PERSON>, val: \u001b[1;36m0.940599\u001b[0m, currency: , accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: EquityWithLoanValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147854.98</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: EquityWithLoanValue-S, val: \u001b[1;36m1147854.98\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidation-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147854.98</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidation-S, val: \u001b[1;36m1147854.98\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationUncertainty, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationUncertainty, val: \u001b[1;36m0.00\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: GrossPositionValue-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">387512.89</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: GrossPositionValue-S, val: \u001b[1;36m387512.89\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: EquityWithLoanValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147854.98</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: EquityWithLoanV<PERSON>ue, val: \u001b[1;36m1147854.98\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidation, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147854.98</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidation, val: \u001b[1;36m1147854.98\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: BuyingPower, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7124392.86</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: BuyingPower, val: \u001b[1;36m7124392.86\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: GrossPositionValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">387512.89</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: GrossPositionValue, val: \u001b[1;36m387512.89\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: InitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79196.05</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: InitMarginReq-S, val: \u001b[1;36m79196.05\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68183.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MaintMarginReq-S, val: \u001b[1;36m68183.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068658.93</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AvailableFunds-S, val: \u001b[1;36m1068658.93\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079671.18</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExcessLiquidity-S, val: \u001b[1;36m1079671.18\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: InitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79196.05</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: InitMarginReq, val: \u001b[1;36m79196.05\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: MaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68183.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: MaintMarginReq, val: \u001b[1;36m68183.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: AvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068658.93</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: AvailableFunds, val: \u001b[1;36m1068658.93\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: ExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079671.18</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: ExcessLiquidity, val: \u001b[1;36m1079671.18\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadInitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79196.05</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadInitMarginReq-S, val: \u001b[1;36m79196.05\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadMaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68183.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadMaintMarginReq-S, val: \u001b[1;36m68183.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadAvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068658.93</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadAvailableFunds-S, val: \u001b[1;36m1068658.93\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079671.18</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadExcessLiquidity-S, val: \u001b[1;36m1079671.18\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadInitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79196.05</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadInitMarginReq, val: \u001b[1;36m79196.05\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadMaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68183.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadMaintMarginReq, val: \u001b[1;36m68183.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadAvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068658.93</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadAvailableFunds, val: \u001b[1;36m1068658.93\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: LookAheadExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079671.18</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: LookAheadExcessLiquidity, val: \u001b[1;36m1079671.18\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullInitMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79196.05</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullInitMarginReq-S, val: \u001b[1;36m79196.05\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullMaintMarginReq-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68183.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullMaintMarginReq-S, val: \u001b[1;36m68183.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullAvailableFunds-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068658.93</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullAvailableFunds-S, val: \u001b[1;36m1068658.93\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullExcessLiquidity-S, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079671.18</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullExcessLiquidity-S, val: \u001b[1;36m1079671.18\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullInitMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79196.05</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullInitMarginReq, val: \u001b[1;36m79196.05\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullMaintMarginReq, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">68183.80</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullMaintMarginReq, val: \u001b[1;36m68183.80\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullAvailableFunds, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1068658.93</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullAvailableFunds, val: \u001b[1;36m1068658.93\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: FullExcessLiquidity, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1079671.18</span>, currency: HKD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: FullExcessLiquidity, val: \u001b[1;36m1079671.18\u001b[0m, currency: HKD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">49313.08</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: StockMarketValue, val: \u001b[1;36m49313.08\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">96869.8742</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m96869.8742\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">13999.53</span>, currency: USD, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m13999.53\u001b[0m, currency: USD, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: StockMarketValue, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">386572.19</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: StockMarketValue, val: \u001b[1;36m386572.19\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: NetLiquidationByCurrency, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1147854.9768</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: NetLiquidationByCurrency, val: \u001b[1;36m1147854.9768\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountValue. key: UnrealizedPnL, val: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">109743.60</span>, currency: BASE, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updateAccountValue. key: UnrealizedPnL, val: \u001b[1;36m109743.60\u001b[0m, currency: BASE, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4815747</span>,NVDA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,NVDA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">136.3639984</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">272.73</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">105.38505</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61.96</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m4815747\u001b[0m,NVDA,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,NVDA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \n", "\u001b[1;36m136.3639984\u001b[0m, marketValue: \u001b[1;36m272.73\u001b[0m, averageCost: \u001b[1;36m105.38505\u001b[0m, unrealizedPNL: \u001b[1;36m61.96\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TLT,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TLT,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">85.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">170.68</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">95.60305</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-20.53</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,TLT,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TLT,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \n", "\u001b[1;36m85.********\u001b[0m, marketValue: \u001b[1;36m170.68\u001b[0m, averageCost: \u001b[1;36m95.60305\u001b[0m, unrealizedPNL: \u001b[1;36m-20.53\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TLH,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,ARCA,USD,TLH,TLH,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">98.6263504</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">197.25</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">102.49</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-7.73</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,TLH,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,ARCA,USD,TLH,TLH,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m2\u001b[0m, marketPrice: \n", "\u001b[1;36m98.6263504\u001b[0m, marketValue: \u001b[1;36m197.25\u001b[0m, averageCost: \u001b[1;36m102.49\u001b[0m, unrealizedPNL: \u001b[1;36m-7.73\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>,TSLA,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TSLA,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">46</span>, \n", "marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">362.4040222</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16670.59</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">257.********</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4838.02</span>, realizedPNL: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m********\u001b[0m,TSLA,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TSLA,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m46\u001b[0m, \n", "marketPrice: \u001b[1;36m362.4040222\u001b[0m, marketValue: \u001b[1;36m16670.59\u001b[0m, averageCost: \u001b[1;36m257.********\u001b[0m, unrealizedPNL: \u001b[1;36m4838.02\u001b[0m, realizedPNL: \n", "\u001b[1;36m0.0\u001b[0m, accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,QQQ,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,QQQ,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">60</span>, marketPrice:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">521.3200073</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31279.2</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">371.94</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8962.8</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,QQQ,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,QQQ,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m60\u001b[0m, marketPrice:\n", "\u001b[1;36m521.3200073\u001b[0m, marketValue: \u001b[1;36m31279.2\u001b[0m, averageCost: \u001b[1;36m371.94\u001b[0m, unrealizedPNL: \u001b[1;36m8962.8\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,PLTR,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,PLTR,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, \n", "marketPrice: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">124.5419159</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">124.54</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.8394</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">33.7</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, \n", "accountName: DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,PLTR,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,PLTR,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m1\u001b[0m, \n", "marketPrice: \u001b[1;36m124.5419159\u001b[0m, marketValue: \u001b[1;36m124.54\u001b[0m, averageCost: \u001b[1;36m90.8394\u001b[0m, unrealizedPNL: \u001b[1;36m33.7\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, \n", "accountName: DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updatePortfolio. contract: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>,TEM,STK,,,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,,,NASDAQ,USD,TEM,NMS,<span style=\"color: #ff0000; text-decoration-color: #ff0000; font-style: italic\">False</span>,,,,combo:, position: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>, marketPrice: \n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54.********</span>, marketValue: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">489.39</span>, averageCost: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39.0671778</span>, unrealizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">137.78</span>, realizedPNL: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>, accountName: \n", "DU7492998\n", "</pre>\n"], "text/plain": ["updatePortfolio. contract: \u001b[1;36m*********\u001b[0m,TEM,STK,,,\u001b[1;36m0\u001b[0m,\u001b[1;36m0\u001b[0m,,,NASDAQ,USD,TEM,NMS,\u001b[3;91mFalse\u001b[0m,,,,combo:, position: \u001b[1;36m9\u001b[0m, marketPrice: \n", "\u001b[1;36m54.********\u001b[0m, marketValue: \u001b[1;36m489.39\u001b[0m, averageCost: \u001b[1;36m39.0671778\u001b[0m, unrealizedPNL: \u001b[1;36m137.78\u001b[0m, realizedPNL: \u001b[1;36m0.0\u001b[0m, accountName: \n", "DU7492998\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">updateAccountTime. timeStamp: <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:35</span>\n", "</pre>\n"], "text/plain": ["updateAccountTime. timeStamp: \u001b[1;92m10:35\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from threading import Thread, Event\n", "from ibapi.wrapper import EWrapper\n", "from ibapi.client import EClient\n", "from ibapi.common import *\n", "from ibapi.contract import Contract\n", "from ibapi.order import Order\n", "from ibapi.order_state import OrderState\n", "\n", "from datetime import datetime\n", "from functools import wraps\n", "\n", "from pandas import DataFrame as df\n", "\n", "from typing import Any\n", "import json \n", "import time\n", "from collections import defaultdict\n", "import inspect\n", "\n", "from rich import print\n", "from rich.traceback import install\n", "\n", "install()\n", "\n", "class IBTool(EClient, EWrapper):\n", "    def __init__(self, config):\n", "        EClient.__init__(self, self)\n", "        self.config = config\n", "        self.errorCodes = {502: \"can't connect to TWS\", 504: \"no connection\",326: \"client id is already in use\"}\n", "        self.nextOrderId = 0\n", "        self.reqId = 1\n", "        self.reponses = {}\n", "        self.accountSummaryData = {}\n", "        self.accountData = {}\n", "        self.accountUpdateDone = Event()\n", "        self.protfolioData = {}\n", "        self.accountUpdateTime = \"\"\n", "        self.openOrderDone  = Event()\n", "        self.openOrdersData = []\n", "        self.reqResMapping = {}\n", "        self.register_reqResMapping(self.reqAccountUpdates, self.accountDownloadEnd)\n", "\n", "    def nextValidId(self, orderId: int):   \n", "        self.nextOrderId = orderId\n", "\n", "    @staticmethod\n", "    def req_ID_wrapper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", "            Logger.log(f\"Requesting {func.__name__}\")\n", "\n", "            if args :\n", "                reqId = args[0]\n", "            elif 'reqId' in kwargs:\n", "                reqId = kwargs['reqId']\n", "            else:\n", "                raise Exception(\"Request Warpper require reqId as first argument\")\n", "            \n", "            response = Response(reqId)\n", "            self.reponses[reqId] = response\n", "            \n", "            self.reqId += 1\n", "            func(self, *args, **kwargs)\n", "            return response\n", "        return wrapper\n", "\n", "    @staticmethod\n", "    def end_callback_ID_wrapper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", "            \n", "          \n", "            Logger.log(f\"EndCallback {func.__name__}\")\n", "\n", "            result = func(self, *args, **kwargs)\n", "            \n", "            if args :\n", "                reqId = args[0]\n", "            elif 'reqId' in kwargs:\n", "                reqId = kwargs['reqId']\n", "            else:\n", "                raise Exception(\"Callback Warpper require reqId as first argument\")\n", "            \n", "            response = self.reponses[reqId]\n", "            response.done.set()\n", "            self.reponses.pop(reqId)\n", "\n", "            return result\n", "        return wrapper\n", "    \n", "\n", "            \n", "    @staticmethod\n", "    def callback_ID_wrapper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", "\n", "            sig = inspect.signature(func)\n", "            bound_args = sig.bind(self, *args, **kwargs)\n", "            bound_args.apply_defaults()\n", "         \n", "            params = dict(bound_args.arguments)\n", "      \n", "            params.pop('self', None)\n", "            \n", " \n", "            if 'reqId' in params:\n", "                reqId = params['reqId']\n", "            else:\n", "    \n", "                reqId = args[0]\n", "\n", "            record = {\"callback\": func.__name__}\n", "            record.update(params)\n", "            \n", "            if reqId not in self.reponses:\n", "                self.reponses[reqId] = Response(reqId)\n", "   \n", "            self.reponses[reqId].contents.append(record)\n", "\n", "            return func(self, *args, **kwargs)\n", "        return wrapper\n", "    \n", "\n", "    @staticmethod\n", "    def req_warpper(func):\n", "        @wraps(func)    \n", "        def wrapper(self, *args, **kwargs):\n", "            Logger.log(f\"x'x'x'x'x'x'x'x'x'x'x'x'x'x'x'xxxxxxxxxxxxxxxxx {func.__name__}\")\n", "            key =  func.__name__\n", "            mapping = self.reqResMapping.get(key)\n", "            if mapping is None:\n", "                raise Exception(f\"Request Warpper require reqResMapping for {key}\")\n", "            \n", "            mapping[\"event\"].clear()\n", "            result = func(self, *args, **kwargs)\n", "\n", "            return mapping\n", "        return wrapper\n", "\n", "\n", "    @staticmethod\n", "    def end_callback_warpper(func):\n", "        @wraps(func)    \n", "        def wrapper(self, *args, **kwargs):\n", "            key =  func.__name__\n", "            mapping = self.reqResMapping.get(key)\n", "            if mapping is None:\n", "                raise Exception(f\"Request Warpper require reqResMapping for {key}\")\n", "            \n", "            mapping[\"event\"].set()\n", "            result = func(self, *args, **kwargs)\n", "            return result\n", "        return wrapper\n", "\n", "\n", "    #This is subscribion update , response.contents will not be used\n", "    @end_callback_ID_wrapper\n", "    def accountSummaryEnd(self, reqId: int):\n", "        response = self.reponses[reqId]\n", "        for content in response.contents:\n", "            if content['account'] not in self.accountSummaryData :\n", "                self.accountSummaryData[content['account']] = {}\n", "            \n", "            self.accountSummaryData[content['account']][content['tag']] = {'currency': content['currency'], 'value': content['value'], 'account': content['account'], 'tag': content['tag']}\n", " \n", "           \n", "        print(  self.accountSummaryData)\n", "\n", "    def historicalData(self, reqId: int, bar: BarData):\n", "        self.reponses[reqId].contents.append({\"reqId\": reqId, \"bar\": bar})\n", "        print(f\"historicalData. reqId: {reqId}, bar: {bar}\")\n", "    \n", "    def historicalDataUpdate(self, reqId: int, bar: BarData):\n", "        #self.reponses[reqId].contents.append({\"reqId\": reqId, \"bar\": bar})\n", "        print(f\"historicalDataUpdate. reqId: {reqId}, bar: {bar}\")\n", "\n", "\n", "\n", "\n", "    def updatePortfolio(self, contract: Contract, position: float,\n", "                        marketPrice: float, marketValue: float,\n", "                        averageCost: float, unrealizedPNL: float,\n", "                        realizedPNL: float, accountName: str):\n", "\n", "        if accountName not in self.protfolioData:\n", "            self.protfolioData[accountName] = {}\n", "        \n", "        self.protfolioData[accountName][contract.conId] = {'contract': contract, 'position': position, 'marketPrice': marketPrice, 'marketValue': marketValue, 'averageCost': averageCost, 'unrealizedPNL': unrealizedPNL, 'realizedPNL': realizedPNL, 'accountName': accountName}\n", "         \n", "        \n", "        print(f\"updatePortfolio. contract: {contract}, position: {position}, marketPrice: {marketPrice}, marketValue: {marketValue}, averageCost: {averageCost}, unrealizedPNL: {unrealizedPNL}, realizedPNL: {realizedPNL}, accountName: {accountName}\")\n", "\n", "\n", "    def updateAccountValue(self, key: str, val: str, currency: str,\n", "                          accountName: str):\n", "        \n", "        if accountName not in self.accountData:\n", "            self.accountData[accountName] = {}\n", "        \n", "        self.accountData[accountName][key] = {'val': val, 'currency': currency, 'accountName': accountName}\n", "       \n", "\n", "        print(f\"updateAccountValue. key: {key}, val: {val}, currency: {currency}, accountName: {accountName}\")\n", "\n", "    def updateAccountTime(self, timeStamp: str):\n", "        self.accountUpdateTime = timeStamp\n", "        print(f\"updateAccountTime. timeStamp: {timeStamp}\")\n", "\n", "\n", "    def connect(self, host, port, clientId):\n", "        \n", "        self.reponses[0] = Response(0)\n", "            \n", "        return super().connect(host, port, clientId)\n", "\n", "    def error(self, reqId: TickerId, errorCode: int, errorString: str, contract: Any = None):\n", "        if reqId == -1:\n", "            reqId = 0\n", "\n", "        response = self.reponses[reqId]\n", "        msg =  {\"reqId\": reqId, \"errorCode\": errorCode, \"errorString\": errorString}\n", "        if str.find(errorString,\"error\") != -1:\n", "            response.errors.append(msg)\n", "        elif str.find(errorString,\"warning\") != -1:\n", "            response.warnings.append(msg)\n", "        else:\n", "            response.infos.append(msg)\n", "        \n", "        print(msg)\n", "\n", "\n", "\n", "    def register_reqResMapping(self,req_func, resp_func):\n", "        mapping = {\n", "            \"request\": req_func,\n", "            \"response\": resp_func,\n", "            \"event\": Event()\n", "        }\n", "       \n", "        self.reqResMapping[req_func.__name__] = mapping\n", "        self.reqResMapping[resp_func.__name__] = mapping\n", "\n", " \n", "\n", "IBTool.reqHistoricalData = IBTool.req_ID_wrapper(IBTool.reqHistoricalData)\n", "IBTool.historicalDataEnd = IBTool.end_callback_ID_wrapper(IBTool.historicalDataEnd)\n", "\n", "IBTool.reqAccountSummary = IBTool.req_ID_wrapper(IBTool.reqAccountSummary)\n", "IBTool.accountSummary = IBTool.callback_ID_wrapper(IBTool.accountSummary)\n", "\n", "\n", "\n", "IBTool.reqAccountUpdates = IBTool.req_warpper(IBTool.reqAccountUpdates)\n", "IBTool.accountDownloadEnd = IBTool.end_callback_warpper(IBTool.accountDownloadEnd)\n", "\n", "\n", "\n", "IBTool.placeOrder = IBTool.req_ID_wrapper(IBTool.placeOrder)\n", "\n", "\n", "class Response():\n", "    def __init__(self, reqId):\n", "        self.reqId = reqId\n", "        self.done = Event()\n", "        self.contents = []\n", "        self.errors = []\n", "        self.warnings = []\n", "        self.infos = []\n", "\n", "\n", "\n", "class Logger():\n", "    def __init__(self, config):\n", "        self.config = config\n", "\n", "    def log(self, message):\n", "        print(message)\n", "\n", "    def log(message):\n", "        print(f\"{datetime.now():%Y-%m-%d %H:%M:%S} {message}\")\n", "\n", "class TradeTool():\n", "    def __init__(self):\n", "        self.config = 'config.json'\n", "        self.ib_Thread = None\n", "        self.ib = None\n", "\n", "        with open(self.config) as f:\n", "            self.config = json.load(f)\n", "\n", "        self.logger = Logger(self.config)\n", "       \n", "    \n", "    def start(self):\n", "        \n", "        self.connect()\n", "\n", "        res = self.ib.reqAccountUpdates(True, \"\")\n", "        res[\"event\"].wait()\n", "\n", "        time.sleep(10)\n", "        \n", "        print(\"finish\")\n", "\n", "\n", "    def connect (self):\n", "        \n", "        attempt = 0\n", "        isConnect = False\n", "        while isConnect == False and attempt < 10:\n", "            Logger.log(\"Attempt Connecting...\")\n", "\n", "            if self.ib != None:\n", "                self.ib.disconnect()\n", "                self.ib = None\n", "                time.sleep(1)\n", "\n", "            self.ib = IBTool(self.config)\n", "            self.ib.connect(self.config['host'], self.config['port'], clientId=self.config['clientId'])\n", "            self.ib_Thread = Thread(target=self.ib.run,args=(), daemon=True)\n", "            self.ib_Thread.start()\n", "            attempt += 1\n", "            time.sleep(2)\n", "\n", "            isConnect = self.ib.isConnected()\n", "            Logger.log(f\"Connection attempt {attempt} : {[\"Not Success\",\"Success\"][isConnect]}\")   \n", "            \n", "\n", "            \n", "tt = TradeTool()\n", "tt.start()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}