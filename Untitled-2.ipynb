{"cells": [{"cell_type": "code", "execution_count": null, "id": "196fd2d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在 req_super_wrapper 裡做額外處理，先執行 req_wrapper 的功能\n", "req_wrapper 內部邏輯\n", "request\n", "req_super_wrapper 裡的額外處理結束\n", "Waiting for response (simulate 2 second delay)...\n", "response: sample data\n", "finish\n", "{'request': <function IB.request at 0x000001D82CB9DE40>, 'response': <function IB.response at 0x000001D82CB9E660>, 'event': <threading.Event at 0x1d82c9c3f80: set>, 'reqId': 1}\n"]}], "source": ["import functools\n", "import threading\n", "import time\n", "\n", "# 全域 mapping 列表，裡面包含 request 與 response 對應的函數與記錄用的 Event\n", "functionMapping = [\n", "    {\n", "        \"request\": None,  # 之後會設定\n", "        \"response\": None,  # 之後會設定\n", "        \"event\": threading.Event()\n", "    }\n", "]\n", "\n", "def getMappingByRequest(request=None, response=None):\n", "    for mapping in functionMapping:\n", "        if request is not None and mapping[\"request\"] is not None and mapping[\"request\"].__name__ == request.__name__:\n", "            return mapping\n", "        elif response is not None and mapping[\"response\"] is not None and mapping[\"response\"].__name__ == response.__name__:\n", "            return mapping\n", "    return None\n", "\n", "def req_super_wrapper(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        print(\"在 req_super_wrapper 裡做額外處理，先執行 req_wrapper 的功能\")\n", "        # 先執行原始的 req_wrapper 裡的邏輯\n", "        # 這裡我們直接使用 req_wrapper(func) 來得到包裝後的函數，再執行它\n", "        ret = req_wrapper(func)(*args, **kwargs)\n", "\n", "        ret[\"reqId\"] = 1\n", "\n", "        # 這邊可以做更多額外處理\n", "        print(\"req_super_wrapper 裡的額外處理結束\")\n", "        return ret\n", "    return wrapper\n", "\n", "def req_wrapper(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        print(\"req_wrapper 內部邏輯\")\n", "        ret = getMappingByRequest(request=func)\n", "        if ret is None:\n", "            raise Exception(f\"req_wrapper: mapping for {func.__name__} not found\")\n", "        ret[\"event\"].clear()\n", "        result = func(*args, **kwargs)\n", "        # 為了方便，可以返回整個 mapping 字典，這樣外層就可以調用 ret[\"event\"].wait()\n", "        return ret\n", "    return wrapper\n", "\n", "def response_wrapper(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        ret = getMappingByRequest(response=func)\n", "        if ret is None:\n", "            raise Exception(f\"response_wrapper: mapping for {func.__name__} not found\")\n", "        result = func(*args, **kwargs)\n", "        ret[\"event\"].set()\n", "        return result\n", "    return wrapper\n", "\n", "class IB:\n", "  \n", "    def request(self):\n", "        print(\"request\")  # 模擬發送請求\n", "        # 可放入其他請求邏輯\n", "        return \"request sent\"\n", "    \n", "  \n", "    def response(self, data):\n", "        print(\"response:\", data)  # 模擬處理回應\n", "        # 可放入其他回應邏輯\n", "        return \"response processed\"\n", "\n", "# 這裡我們在 mapping 裡設定 IB.request 與 IB.response\n", "# 注意：IB.request 與 IB.response 此時是裝飾後的函數（wrapper），\n", "# 但我們會根據 __name__ 來比較，所以只要名字相同就能匹配。\n", "functionMapping[0][\"request\"] = IB.request\n", "functionMapping[0][\"response\"] = IB.response\n", "\n", "\n", "IB.request = req_super_wrapper(IB.request)\n", "IB.response = response_wrapper(IB.response)\n", "\n", "\n", "if __name__ == '__main__':\n", "    ib = IB()\n", "    \n", "    # 呼叫 request 方法，包裝器會先清除 Event 後發出請求，並返回 mapping\n", "    ret = ib.request()\n", "    print(\"Waiting for response (simulate 2 second delay)...\")\n", "    \n", "    # 模擬 2 秒後回應產生（實際情況通常來自事件驅動回調）\n", "    def simulate_response():\n", "        time.sleep(2)\n", "        ib.response(\"sample data\")\n", "    \n", "    threading.Thread(target=simulate_response).start()\n", "    \n", "    # 等待最多 5 秒，如果事件被設置（set）則 wait() 返回\n", "    ret[\"event\"].wait(5)\n", "    print(\"finish\")\n", "\n", "   \n"]}, {"cell_type": "code", "execution_count": 2, "id": "5f811e3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在 req_super_wrapper 裡做額外處理，先執行 req_wrapper 的功能\n", "原始 req_wrapper 先執行\n", "my_request 執行\n", "req_super_wrapper 裡的額外處理結束\n"]}, {"data": {"text/plain": ["'done'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["def req_wrapper(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        print(\"原始 req_wrapper 先執行\")\n", "        ret = func(*args, **kwargs)\n", "        return ret\n", "    return wrapper\n", "def req_super_wrapper(func):\n", "    @functools.wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        print(\"在 req_super_wrapper 裡做額外處理，先執行 req_wrapper 的功能\")\n", "        # 先執行原始的 req_wrapper 裡的邏輯\n", "        # 這裡我們直接使用 req_wrapper(func) 來得到包裝後的函數，再執行它\n", "        ret = req_wrapper(func)(*args, **kwargs)\n", "        # 這邊可以做更多額外處理\n", "        print(\"req_super_wrapper 裡的額外處理結束\")\n", "        return ret\n", "    return wrapper\n", "@req_super_wrapper\n", "def my_request():\n", "    print(\"my_request 執行\")\n", "    return \"done\"\n", "\n", "my_request()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c6e96e71", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "True\n"]}, {"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'clear'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 13\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28mprint\u001b[39m(event\u001b[38;5;241m.\u001b[39mis_set())  \u001b[38;5;66;03m# Output: True\u001b[39;00m\n\u001b[0;32m     11\u001b[0m event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m  \n\u001b[1;32m---> 13\u001b[0m \u001b[43mevent\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclear\u001b[49m()\n\u001b[0;32m     14\u001b[0m \u001b[38;5;28mprint\u001b[39m(event\u001b[38;5;241m.\u001b[39mis_set())  \u001b[38;5;66;03m# Output: False\u001b[39;00m\n", "\u001b[1;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'clear'"]}], "source": ["from threading import Event\n", "\n", "event = Event()\n", "\n", "# Initially, the event is not set\n", "print(event.is_set())  # Output: False\n", "\n", "# Set the event\n", "event.set()\n", "print(event.is_set())  # Output: True\n", "event = None  \n", "\n", "event.clear()\n", "print(event.is_set())  # Output: False"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}