{"cells": [{"cell_type": "code", "execution_count": null, "id": "a28b1ba9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["xxxError:  -1   2104   Market data farm connection is OK:usfarm.nj\n", "xxxError:  -1   2104   Market data farm connection is OK:jfarm\n", "xxxError:  -1   2104   Market data farm connection is OK:cafarm\n", "xxxError:  -1   2104   Market data farm connection is OK:cashfarm\n", "xxxError:  -1   2104   Market data farm connection is OK:usfarm\n", "xxxError:  -1   2106   HMDS data farm connection is OK:euhmds\n", "xxxError:  -1   2106   HMDS data farm connection is OK:fundfarm\n", "xxxError:  -1   2106   HMDS data farm connection is OK:ushmds\n", "xxxError:  -1   2158   Sec-def data farm connection is OK:secdefil\n", "1748356592.532641\n", "20250527 04:00:0\n", "Date: 20250527 04:00:00 US/Eastern, Open: 346.5, High: 348.9, Low: 345.22, Close: 347.47, Volume: 33481, WAP: 347.476, BarCount: 210\n", "20250527 04:01:0\n", "Date: 20250527 04:01:00 US/Eastern, Open: 347.4, High: 348.25, Low: 347.4, Close: 348.1, Volume: 13605, WAP: 347.969, BarCount: 90\n", "20250527 04:02:0\n", "Date: 20250527 04:02:00 US/Eastern, Open: 348.3, High: 348.76, Low: 347.85, Close: 347.92, Volume: 14985, WAP: 348.28, BarCount: 80\n", "20250527 04:03:0\n", "Date: 20250527 04:03:00 US/Eastern, Open: 347.9, High: 348.21, Low: 347.81, Close: 347.85, Volume: 2726, WAP: 347.966, BarCount: 22\n", "20250527 04:04:0\n", "Date: 20250527 04:04:00 US/Eastern, Open: 347.88, High: 348.15, Low: 347.75, Close: 348.08, Volume: 6036, WAP: 347.94, BarCount: 34\n", "20250527 04:05:0\n", "Date: 20250527 04:05:00 US/Eastern, Open: 348.06, High: 348.27, Low: 347.84, Close: 348.15, Volume: 3618, WAP: 348.071, BarCount: 28\n", "20250527 04:06:0\n", "Date: 20250527 04:06:00 US/Eastern, Open: 348.16, High: 348.75, Low: 348.16, Close: 348.7, Volume: 5876, WAP: 348.418, BarCount: 39\n", "20250527 04:07:0\n", "Date: 20250527 04:07:00 US/Eastern, Open: 348.75, High: 349, Low: 348.69, Close: 348.94, Volume: 7768, WAP: 348.898, BarCount: 44\n", "20250527 04:08:0\n", "Date: 20250527 04:08:00 US/Eastern, Open: 348.9, High: 349.63, Low: 348.9, Close: 349.63, Volume: 20659, WAP: 349.147, BarCount: 90\n", "20250527 04:09:0\n", "Date: 20250527 04:09:00 US/Eastern, Open: 349.61, High: 349.8, Low: 349.47, Close: 349.57, Volume: 6834, WAP: 349.644, BarCount: 56\n", "20250527 04:10:0\n", "Date: 20250527 04:10:00 US/Eastern, Open: 349.44, High: 349.56, Low: 349.2, Close: 349.41, Volume: 7294, WAP: 349.375, BarCount: 45\n", "20250527 04:11:0\n", "Date: 20250527 04:11:00 US/Eastern, Open: 349.35, High: 349.48, Low: 348.96, Close: 349, Volume: 7531, WAP: 349.123, BarCount: 42\n", "20250527 04:12:0\n", "Date: 20250527 04:12:00 US/Eastern, Open: 348.86, High: 348.86, Low: 348.72, Close: 348.85, Volume: 4880, WAP: 348.807, BarCount: 25\n", "20250527 04:13:0\n", "Date: 20250527 04:13:00 US/Eastern, Open: 348.98, High: 349.25, Low: 348.9, Close: 349.19, Volume: 3203, WAP: 349.02, BarCount: 27\n", "20250527 04:14:0\n", "Date: 20250527 04:14:00 US/Eastern, Open: 349.15, High: 349.15, Low: 348.86, Close: 349, Volume: 5020, WAP: 348.977, BarCount: 36\n", "20250527 04:15:0\n", "Date: 20250527 04:15:00 US/Eastern, Open: 348.9, High: 348.99, Low: 348.8, Close: 348.8, Volume: 3166, WAP: 348.871, BarCount: 13\n", "20250527 04:16:0\n", "Date: 20250527 04:16:00 US/Eastern, Open: 348.82, High: 348.82, Low: 348.55, Close: 348.64, Volume: 3039, WAP: 348.698, BarCount: 21\n", "20250527 04:17:0\n", "Date: 20250527 04:17:00 US/Eastern, Open: 348.64, High: 349.02, Low: 348.64, Close: 348.99, Volume: 2142, WAP: 348.888, BarCount: 14\n", "20250527 04:18:0\n", "Date: 20250527 04:18:00 US/Eastern, Open: 348.92, High: 348.92, Low: 348.64, Close: 348.84, Volume: 3118, WAP: 348.78, BarCount: 20\n", "20250527 04:19:0\n", "Date: 20250527 04:19:00 US/Eastern, Open: 348.8, High: 348.8, Low: 348.69, Close: 348.69, Volume: 400, WAP: 348.74, BarCount: 4\n", "20250527 04:20:0\n", "Date: 20250527 04:20:00 US/Eastern, Open: 348.67, High: 348.68, Low: 348.38, Close: 348.4, Volume: 6345, WAP: 348.519, BarCount: 35\n", "20250527 04:21:0\n", "Date: 20250527 04:21:00 US/Eastern, Open: 348.43, High: 348.65, Low: 348.43, Close: 348.57, Volume: 4250, WAP: 348.513, BarCount: 22\n", "20250527 04:22:0\n", "Date: 20250527 04:22:00 US/Eastern, Open: 348.6, High: 348.79, Low: 348.56, Close: 348.79, Volume: 2706, WAP: 348.61, BarCount: 14\n", "20250527 04:23:0\n", "Date: 20250527 04:23:00 US/Eastern, Open: 348.76, High: 348.95, Low: 348.75, Close: 348.95, Volume: 1432, WAP: 348.862, BarCount: 11\n", "20250527 04:24:0\n", "Date: 20250527 04:24:00 US/Eastern, Open: 348.88, High: 348.88, Low: 348.75, Close: 348.75, Volume: 1010, WAP: 348.8, BarCount: 9\n", "20250527 04:25:0\n", "Date: 20250527 04:25:00 US/Eastern, Open: 348.92, High: 348.92, Low: 348.88, Close: 348.88, Volume: 400, WAP: 348.898, BarCount: 4\n", "20250527 04:26:0\n", "Date: 20250527 04:26:00 US/Eastern, Open: 348.76, High: 348.85, Low: 348.75, Close: 348.75, Volume: 600, WAP: 348.78, BarCount: 6\n", "20250527 04:27:0\n", "Date: 20250527 04:27:00 US/Eastern, Open: 348.75, High: 348.95, Low: 348.75, Close: 348.92, Volume: 752, WAP: 348.887, BarCount: 7\n", "20250527 04:28:0\n", "Date: 20250527 04:28:00 US/Eastern, Open: 348.97, High: 349, Low: 348.91, Close: 348.95, Volume: 1810, WAP: 348.975, BarCount: 16\n", "20250527 04:29:0\n", "Date: 20250527 04:29:00 US/Eastern, Open: 348.8, High: 348.81, Low: 348.73, Close: 348.76, Volume: 800, WAP: 348.755, BarCount: 7\n", "20250527 04:30:0\n", "Date: 20250527 04:30:00 US/Eastern, Open: 348.81, High: 348.9, Low: 348.73, Close: 348.73, Volume: 700, WAP: 348.81, BarCount: 7\n", "20250527 04:31:0\n", "Date: 20250527 04:31:00 US/Eastern, Open: 348.61, High: 348.61, Low: 348.38, Close: 348.45, Volume: 3704, WAP: 348.525, BarCount: 19\n", "20250527 04:32:0\n", "Date: 20250527 04:32:00 US/Eastern, Open: 348.4, High: 348.5, Low: 348.35, Close: 348.5, Volume: 1228, WAP: 348.464, BarCount: 12\n", "20250527 04:33:0\n", "Date: 20250527 04:33:00 US/Eastern, Open: 348.42, High: 348.42, Low: 348.29, Close: 348.36, Volume: 1715, WAP: 348.338, BarCount: 11\n", "20250527 04:34:0\n", "Date: 20250527 04:34:00 US/Eastern, Open: 348.4, High: 348.5, Low: 348.24, Close: 348.3, Volume: 4129, WAP: 348.376, BarCount: 27\n", "20250527 04:35:0\n", "Date: 20250527 04:35:00 US/Eastern, Open: 348.27, High: 348.27, Low: 348.03, Close: 348.17, Volume: 1977, WAP: 348.169, BarCount: 17\n", "20250527 04:36:0\n", "Date: 20250527 04:36:00 US/Eastern, Open: 348.09, High: 348.19, Low: 348.06, Close: 348.08, Volume: 1243, WAP: 348.104, BarCount: 10\n", "20250527 04:37:0\n", "Date: 20250527 04:37:00 US/Eastern, Open: 348.12, High: 348.19, Low: 348.08, Close: 348.19, Volume: 3583, WAP: 348.158, BarCount: 22\n", "20250527 04:38:0\n", "Date: 20250527 04:38:00 US/Eastern, Open: 348.23, High: 348.23, Low: 348.1, Close: 348.12, Volume: 1830, WAP: 348.161, BarCount: 16\n", "20250527 04:39:0\n", "Date: 20250527 04:39:00 US/Eastern, Open: 348.12, High: 348.12, Low: 347.9, Close: 347.91, Volume: 4281, WAP: 348.017, BarCount: 24\n", "20250527 04:40:0\n", "Date: 20250527 04:40:00 US/Eastern, Open: 347.91, High: 348.14, Low: 347.85, Close: 348.14, Volume: 2117, WAP: 347.932, BarCount: 16\n", "20250527 04:41:0\n", "Date: 20250527 04:41:00 US/Eastern, Open: 348.09, High: 348.24, Low: 348.09, Close: 348.19, Volume: 1458, WAP: 348.186, BarCount: 10\n", "20250527 04:42:0\n", "Date: 20250527 04:42:00 US/Eastern, Open: 348.18, High: 348.52, Low: 348.18, Close: 348.52, Volume: 1053, WAP: 348.411, BarCount: 9\n", "20250527 04:43:0\n", "Date: 20250527 04:43:00 US/Eastern, Open: 348.55, High: 348.62, Low: 348.5, Close: 348.58, Volume: 1148, WAP: 348.554, BarCount: 9\n", "20250527 04:44:0\n", "Date: 20250527 04:44:00 US/Eastern, Open: 348.59, High: 348.73, Low: 348.49, Close: 348.73, Volume: 2618, WAP: 348.525, BarCount: 11\n", "20250527 04:45:0\n", "Date: 20250527 04:45:00 US/Eastern, Open: 348.73, High: 348.77, Low: 348.5, Close: 348.5, Volume: 3483, WAP: 348.679, BarCount: 13\n", "20250527 04:46:0\n", "Date: 20250527 04:46:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.14, Close: 348.14, Volume: 1654, WAP: 348.384, BarCount: 9\n", "20250527 04:47:0\n", "Date: 20250527 04:47:00 US/Eastern, Open: 348.07, High: 348.14, Low: 348.03, Close: 348.03, Volume: 700, WAP: 348.059, BarCount: 6\n", "20250527 04:48:0\n", "Date: 20250527 04:48:00 US/Eastern, Open: 348, High: 348.06, Low: 347.94, Close: 348.06, Volume: 469, WAP: 347.981, BarCount: 4\n", "20250527 04:49:0\n", "Date: 20250527 04:49:00 US/Eastern, Open: 348.04, High: 348.04, Low: 347.8, Close: 347.8, Volume: 3140, WAP: 347.901, BarCount: 17\n", "20250527 04:50:0\n", "Date: 20250527 04:50:00 US/Eastern, Open: 347.83, High: 347.83, Low: 347.69, Close: 347.75, Volume: 3036, WAP: 347.753, BarCount: 22\n", "20250527 04:51:0\n", "Date: 20250527 04:51:00 US/Eastern, Open: 347.71, High: 348.03, Low: 347.67, Close: 347.93, Volume: 2142, WAP: 347.863, BarCount: 15\n", "20250527 04:52:0\n", "Date: 20250527 04:52:00 US/Eastern, Open: 347.85, High: 348.1, Low: 347.85, Close: 348.1, Volume: 4303, WAP: 347.954, BarCount: 24\n", "20250527 04:53:0\n", "Date: 20250527 04:53:00 US/Eastern, Open: 348.08, High: 348.08, Low: 347.96, Close: 347.96, Volume: 200, WAP: 348.02, BarCount: 2\n", "20250527 04:54:0\n", "Date: 20250527 04:54:00 US/Eastern, Open: 347.97, High: 347.97, Low: 347.91, Close: 347.91, Volume: 1140, WAP: 347.922, BarCount: 6\n", "20250527 04:55:0\n", "Date: 20250527 04:55:00 US/Eastern, Open: 347.83, High: 347.83, Low: 347.79, Close: 347.79, Volume: 877, WAP: 347.807, BarCount: 7\n", "20250527 04:56:0\n", "Date: 20250527 04:56:00 US/Eastern, Open: 347.8, High: 348.05, Low: 347.79, Close: 348, Volume: 1745, WAP: 347.904, BarCount: 13\n", "20250527 04:57:0\n", "Date: 20250527 04:57:00 US/Eastern, Open: 348, High: 348, Low: 347.84, Close: 347.84, Volume: 2221, WAP: 347.904, BarCount: 10\n", "20250527 04:58:0\n", "Date: 20250527 04:58:00 US/Eastern, Open: 347.88, High: 347.91, Low: 347.75, Close: 347.75, Volume: 926, WAP: 347.847, BarCount: 6\n", "20250527 04:59:0\n", "Date: 20250527 04:59:00 US/Eastern, Open: 347.88, High: 347.88, Low: 347.85, Close: 347.85, Volume: 1214, WAP: 347.876, BarCount: 5\n", "20250527 05:00:0\n", "Date: 20250527 05:00:00 US/Eastern, Open: 347.85, High: 347.85, Low: 347.85, Close: 347.85, Volume: 0, WAP: 347.85, BarCount: 0\n", "20250527 05:01:0\n", "Date: 20250527 05:01:00 US/Eastern, Open: 347.92, High: 348, Low: 347.92, Close: 348, Volume: 1606, WAP: 347.992, BarCount: 10\n", "20250527 05:02:0\n", "Date: 20250527 05:02:00 US/Eastern, Open: 348.18, High: 348.4, Low: 348.14, Close: 348.14, Volume: 2415, WAP: 348.242, BarCount: 15\n", "20250527 05:03:0\n", "Date: 20250527 05:03:00 US/Eastern, Open: 348.18, High: 348.26, Low: 348.1, Close: 348.14, Volume: 1616, WAP: 348.15, BarCount: 10\n", "20250527 05:04:0\n", "Date: 20250527 05:04:00 US/Eastern, Open: 348.3, High: 348.33, Low: 348.3, Close: 348.33, Volume: 597, WAP: 348.321, BarCount: 2\n", "20250527 05:05:0\n", "Date: 20250527 05:05:00 US/Eastern, Open: 348.36, High: 348.36, Low: 348.25, Close: 348.25, Volume: 300, WAP: 348.29, BarCount: 3\n", "20250527 05:06:0\n", "Date: 20250527 05:06:00 US/Eastern, Open: 348.2, High: 348.26, Low: 348.2, Close: 348.26, Volume: 320, WAP: 348.231, BarCount: 3\n", "20250527 05:07:0\n", "Date: 20250527 05:07:00 US/Eastern, Open: 348.3, High: 348.36, Low: 348.3, Close: 348.35, Volume: 326, WAP: 348.334, BarCount: 3\n", "20250527 05:08:0\n", "Date: 20250527 05:08:00 US/Eastern, Open: 348.44, High: 348.57, Low: 348.44, Close: 348.57, Volume: 1364, WAP: 348.493, BarCount: 8\n", "20250527 05:09:0\n", "Date: 20250527 05:09:00 US/Eastern, Open: 348.5, High: 348.74, Low: 348.4, Close: 348.63, Volume: 4893, WAP: 348.561, BarCount: 36\n", "20250527 05:10:0\n", "Date: 20250527 05:10:00 US/Eastern, Open: 348.68, High: 348.73, Low: 348.68, Close: 348.73, Volume: 1230, WAP: 348.71, BarCount: 4\n", "20250527 05:11:0\n", "Date: 20250527 05:11:00 US/Eastern, Open: 348.7, High: 348.7, Low: 348.62, Close: 348.62, Volume: 1224, WAP: 348.639, BarCount: 10\n", "20250527 05:12:0\n", "Date: 20250527 05:12:00 US/Eastern, Open: 348.64, High: 348.64, Low: 348.5, Close: 348.5, Volume: 387, WAP: 348.572, BarCount: 3\n", "20250527 05:13:0\n", "Date: 20250527 05:13:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.4, Close: 348.5, Volume: 1268, WAP: 348.489, BarCount: 9\n", "20250527 05:14:0\n", "Date: 20250527 05:14:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.5, Close: 348.5, Volume: 0, WAP: 348.5, BarCount: 0\n", "20250527 05:15:0\n", "Date: 20250527 05:15:00 US/Eastern, Open: 348.46, High: 348.46, Low: 348.37, Close: 348.37, Volume: 600, WAP: 348.401, BarCount: 5\n", "20250527 05:16:0\n", "Date: 20250527 05:16:00 US/Eastern, Open: 348.35, High: 348.39, Low: 348.35, Close: 348.35, Volume: 350, WAP: 348.367, BarCount: 3\n", "20250527 05:17:0\n", "Date: 20250527 05:17:00 US/Eastern, Open: 348.35, High: 348.4, Low: 348.32, Close: 348.32, Volume: 720, WAP: 348.366, BarCount: 5\n", "20250527 05:18:0\n", "Date: 20250527 05:18:00 US/Eastern, Open: 348.32, High: 348.45, Low: 348.32, Close: 348.4, Volume: 585, WAP: 348.38, BarCount: 4\n", "20250527 05:19:0\n", "Date: 20250527 05:19:00 US/Eastern, Open: 348.45, High: 348.45, Low: 348.4, Close: 348.43, Volume: 548, WAP: 348.424, BarCount: 5\n", "20250527 05:20:0\n", "Date: 20250527 05:20:00 US/Eastern, Open: 348.38, High: 348.38, Low: 348.3, Close: 348.38, Volume: 702, WAP: 348.346, BarCount: 6\n", "20250527 05:21:0\n", "Date: 20250527 05:21:00 US/Eastern, Open: 348.38, High: 348.38, Low: 348.24, Close: 348.24, Volume: 369, WAP: 348.342, BarCount: 3\n", "20250527 05:22:0\n", "Date: 20250527 05:22:00 US/Eastern, Open: 348.24, High: 348.38, Low: 348.24, Close: 348.34, Volume: 2402, WAP: 348.305, BarCount: 12\n", "20250527 05:23:0\n", "Date: 20250527 05:23:00 US/Eastern, Open: 348.3, High: 348.32, Low: 348.3, Close: 348.32, Volume: 2390, WAP: 348.301, BarCount: 8\n", "20250527 05:24:0\n", "Date: 20250527 05:24:00 US/Eastern, Open: 348.28, High: 348.34, Low: 348.23, Close: 348.23, Volume: 817, WAP: 348.298, BarCount: 5\n", "20250527 05:25:0\n", "Date: 20250527 05:25:00 US/Eastern, Open: 348.26, High: 348.31, Low: 348.2, Close: 348.28, Volume: 1226, WAP: 348.247, BarCount: 8\n", "20250527 05:26:0\n", "Date: 20250527 05:26:00 US/Eastern, Open: 348.25, High: 348.3, Low: 348.22, Close: 348.3, Volume: 380, WAP: 348.255, BarCount: 3\n", "20250527 05:27:0\n", "Date: 20250527 05:27:00 US/Eastern, Open: 348.27, High: 348.3, Low: 348.25, Close: 348.3, Volume: 1190, WAP: 348.261, BarCount: 3\n", "20250527 05:28:0\n", "Date: 20250527 05:28:00 US/Eastern, Open: 348.35, High: 348.4, Low: 348.35, Close: 348.37, Volume: 1319, WAP: 348.36, BarCount: 8\n", "20250527 05:29:0\n", "Date: 20250527 05:29:00 US/Eastern, Open: 348.36, High: 348.36, Low: 348.14, Close: 348.26, Volume: 4151, WAP: 348.207, BarCount: 21\n", "20250527 05:30:0\n", "Date: 20250527 05:30:00 US/Eastern, Open: 348.21, High: 348.22, Low: 348.19, Close: 348.2, Volume: 1723, WAP: 348.204, BarCount: 8\n", "20250527 05:31:0\n", "Date: 20250527 05:31:00 US/Eastern, Open: 348.21, High: 348.22, Low: 348.14, Close: 348.14, Volume: 1567, WAP: 348.208, BarCount: 7\n", "20250527 05:32:0\n", "Date: 20250527 05:32:00 US/Eastern, Open: 348.16, High: 348.28, Low: 348.15, Close: 348.26, Volume: 1637, WAP: 348.202, BarCount: 11\n", "20250527 05:33:0\n", "Date: 20250527 05:33:00 US/Eastern, Open: 348.5, High: 348.62, Low: 348.5, Close: 348.62, Volume: 2757, WAP: 348.52, BarCount: 9\n", "20250527 05:34:0\n", "Date: 20250527 05:34:00 US/Eastern, Open: 348.51, High: 348.52, Low: 348.5, Close: 348.5, Volume: 1725, WAP: 348.506, BarCount: 7\n", "20250527 05:35:0\n", "Date: 20250527 05:35:00 US/Eastern, Open: 348.56, High: 348.56, Low: 348.56, Close: 348.56, Volume: 100, WAP: 348.56, BarCount: 1\n", "20250527 05:36:0\n", "Date: 20250527 05:36:00 US/Eastern, Open: 348.49, High: 348.55, Low: 348.38, Close: 348.38, Volume: 3103, WAP: 348.508, BarCount: 17\n", "20250527 05:37:0\n", "Date: 20250527 05:37:00 US/Eastern, Open: 348.45, High: 348.48, Low: 348.3, Close: 348.3, Volume: 509, WAP: 348.386, BarCount: 5\n", "20250527 05:38:0\n", "Date: 20250527 05:38:00 US/Eastern, Open: 348.48, High: 348.56, Low: 348.48, Close: 348.56, Volume: 1515, WAP: 348.518, BarCount: 11\n", "20250527 05:39:0\n", "Date: 20250527 05:39:00 US/Eastern, Open: 348.5, High: 348.6, Low: 348.46, Close: 348.6, Volume: 587, WAP: 348.51, BarCount: 5\n", "20250527 05:40:0\n", "Date: 20250527 05:40:00 US/Eastern, Open: 348.61, High: 348.74, Low: 348.61, Close: 348.73, Volume: 1321, WAP: 348.684, BarCount: 12\n", "20250527 05:41:0\n", "Date: 20250527 05:41:00 US/Eastern, Open: 348.71, High: 348.71, Low: 348.5, Close: 348.5, Volume: 1228, WAP: 348.54, BarCount: 6\n", "20250527 05:42:0\n", "Date: 20250527 05:42:00 US/Eastern, Open: 348.68, High: 348.68, Low: 348.65, Close: 348.68, Volume: 724, WAP: 348.662, BarCount: 3\n", "20250527 05:43:0\n", "Date: 20250527 05:43:00 US/Eastern, Open: 348.5, High: 348.51, Low: 348.47, Close: 348.51, Volume: 3960, WAP: 348.501, BarCount: 24\n", "20250527 05:44:0\n", "Date: 20250527 05:44:00 US/Eastern, Open: 348.51, High: 348.51, Low: 348.44, Close: 348.45, Volume: 1477, WAP: 348.478, BarCount: 11\n", "20250527 05:45:0\n", "Date: 20250527 05:45:00 US/Eastern, Open: 348.6, High: 348.6, Low: 348.6, Close: 348.6, Volume: 200, WAP: 348.6, BarCount: 2\n", "20250527 05:46:0\n", "Date: 20250527 05:46:00 US/Eastern, Open: 348.62, High: 348.62, Low: 348.54, Close: 348.54, Volume: 363, WAP: 348.598, BarCount: 3\n", "20250527 05:47:0\n", "Date: 20250527 05:47:00 US/Eastern, Open: 348.58, High: 348.59, Low: 348.5, Close: 348.5, Volume: 500, WAP: 348.542, BarCount: 4\n", "20250527 05:48:0\n", "Date: 20250527 05:48:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.38, Close: 348.4, Volume: 1740, WAP: 348.44, BarCount: 13\n", "20250527 05:49:0\n", "Date: 20250527 05:49:00 US/Eastern, Open: 348.35, High: 348.39, Low: 348.35, Close: 348.39, Volume: 500, WAP: 348.368, BarCount: 5\n", "20250527 05:50:0\n", "Date: 20250527 05:50:00 US/Eastern, Open: 348.44, High: 348.44, Low: 348.44, Close: 348.44, Volume: 100, WAP: 348.44, BarCount: 1\n", "20250527 05:51:0\n", "Date: 20250527 05:51:00 US/Eastern, Open: 348.45, High: 348.45, Low: 348.4, Close: 348.4, Volume: 517, WAP: 348.415, BarCount: 4\n", "20250527 05:52:0\n", "Date: 20250527 05:52:00 US/Eastern, Open: 348.47, High: 348.47, Low: 348.35, Close: 348.35, Volume: 807, WAP: 348.373, BarCount: 6\n", "20250527 05:53:0\n", "Date: 20250527 05:53:00 US/Eastern, Open: 348.37, High: 348.37, Low: 348.3, Close: 348.31, Volume: 1283, WAP: 348.334, BarCount: 9\n", "20250527 05:54:0\n", "Date: 20250527 05:54:00 US/Eastern, Open: 348.26, High: 348.3, Low: 348.23, Close: 348.23, Volume: 1050, WAP: 348.255, BarCount: 9\n", "20250527 05:55:0\n", "Date: 20250527 05:55:00 US/Eastern, Open: 348.14, High: 348.14, Low: 348.07, Close: 348.07, Volume: 782, WAP: 348.105, BarCount: 7\n", "20250527 05:56:0\n", "Date: 20250527 05:56:00 US/Eastern, Open: 348.23, High: 348.3, Low: 348.23, Close: 348.28, Volume: 1420, WAP: 348.266, BarCount: 11\n", "20250527 05:57:0\n", "Date: 20250527 05:57:00 US/Eastern, Open: 348.31, High: 348.33, Low: 348.28, Close: 348.28, Volume: 500, WAP: 348.308, BarCount: 4\n", "20250527 05:58:0\n", "Date: 20250527 05:58:00 US/Eastern, Open: 348.36, High: 348.36, Low: 348.31, Close: 348.31, Volume: 700, WAP: 348.33, BarCount: 6\n", "20250527 05:59:0\n", "Date: 20250527 05:59:00 US/Eastern, Open: 348.31, High: 348.31, Low: 348.29, Close: 348.29, Volume: 547, WAP: 348.305, BarCount: 2\n", "20250527 06:00:0\n", "Date: 20250527 06:00:00 US/Eastern, Open: 348.36, High: 348.36, Low: 348.24, Close: 348.29, Volume: 487, WAP: 348.298, BarCount: 4\n", "20250527 06:01:0\n", "Date: 20250527 06:01:00 US/Eastern, Open: 348.14, High: 348.35, Low: 348.03, Close: 348.32, Volume: 3217, WAP: 348.171, BarCount: 21\n", "20250527 06:02:0\n", "Date: 20250527 06:02:00 US/Eastern, Open: 348.14, High: 348.27, Low: 348.12, Close: 348.2, Volume: 3377, WAP: 348.245, BarCount: 18\n", "20250527 06:03:0\n", "Date: 20250527 06:03:00 US/Eastern, Open: 348.3, High: 348.51, Low: 348.3, Close: 348.51, Volume: 3111, WAP: 348.381, BarCount: 16\n", "20250527 06:04:0\n", "Date: 20250527 06:04:00 US/Eastern, Open: 348.56, High: 348.57, Low: 348.5, Close: 348.57, Volume: 1937, WAP: 348.514, BarCount: 8\n", "20250527 06:05:0\n", "Date: 20250527 06:05:00 US/Eastern, Open: 348.61, High: 348.65, Low: 348.6, Close: 348.65, Volume: 2597, WAP: 348.62, BarCount: 13\n", "20250527 06:06:0\n", "Date: 20250527 06:06:00 US/Eastern, Open: 348.5, High: 348.61, Low: 348.5, Close: 348.58, Volume: 682, WAP: 348.528, BarCount: 4\n", "20250527 06:07:0\n", "Date: 20250527 06:07:00 US/Eastern, Open: 348.59, High: 348.6, Low: 348.59, Close: 348.6, Volume: 200, WAP: 348.595, BarCount: 2\n", "20250527 06:08:0\n", "Date: 20250527 06:08:00 US/Eastern, Open: 348.5, High: 348.6, Low: 348.46, Close: 348.59, Volume: 1956, WAP: 348.519, BarCount: 13\n", "20250527 06:09:0\n", "Date: 20250527 06:09:00 US/Eastern, Open: 348.53, High: 348.53, Low: 348.29, Close: 348.29, Volume: 650, WAP: 348.382, BarCount: 6\n", "20250527 06:10:0\n", "Date: 20250527 06:10:00 US/Eastern, Open: 348.3, High: 348.4, Low: 348.28, Close: 348.33, Volume: 3861, WAP: 348.317, BarCount: 13\n", "20250527 06:11:0\n", "Date: 20250527 06:11:00 US/Eastern, Open: 348.27, High: 348.27, Low: 348, Close: 348.06, Volume: 4905, WAP: 348.079, BarCount: 25\n", "20250527 06:12:0\n", "Date: 20250527 06:12:00 US/Eastern, Open: 348.08, High: 348.08, Low: 348.01, Close: 348.06, Volume: 1836, WAP: 348.059, BarCount: 13\n", "20250527 06:13:0\n", "Date: 20250527 06:13:00 US/Eastern, Open: 348.2, High: 348.35, Low: 348.18, Close: 348.23, Volume: 2920, WAP: 348.277, BarCount: 18\n", "20250527 06:14:0\n", "Date: 20250527 06:14:00 US/Eastern, Open: 348.25, High: 348.25, Low: 348.19, Close: 348.19, Volume: 300, WAP: 348.213, BarCount: 3\n", "20250527 06:15:0\n", "Date: 20250527 06:15:00 US/Eastern, Open: 348.35, High: 348.37, Low: 348.33, Close: 348.33, Volume: 571, WAP: 348.356, BarCount: 5\n", "20250527 06:16:0\n", "Date: 20250527 06:16:00 US/Eastern, Open: 348.32, High: 348.37, Low: 348.32, Close: 348.37, Volume: 308, WAP: 348.347, BarCount: 3\n", "20250527 06:17:0\n", "Date: 20250527 06:17:00 US/Eastern, Open: 348.48, High: 348.5, Low: 348.42, Close: 348.5, Volume: 881, WAP: 348.48, BarCount: 6\n", "20250527 06:18:0\n", "Date: 20250527 06:18:00 US/Eastern, Open: 348.45, High: 348.45, Low: 348.44, Close: 348.44, Volume: 250, WAP: 348.444, BarCount: 2\n", "20250527 06:19:0\n", "Date: 20250527 06:19:00 US/Eastern, Open: 348.39, High: 348.44, Low: 348.39, Close: 348.44, Volume: 811, WAP: 348.432, BarCount: 3\n", "20250527 06:20:0\n", "Date: 20250527 06:20:00 US/Eastern, Open: 348.47, High: 348.48, Low: 348.4, Close: 348.4, Volume: 550, WAP: 348.456, BarCount: 5\n", "20250527 06:21:0\n", "Date: 20250527 06:21:00 US/Eastern, Open: 348.35, High: 348.35, Low: 348.35, Close: 348.35, Volume: 120, WAP: 348.35, BarCount: 1\n", "20250527 06:22:0\n", "Date: 20250527 06:22:00 US/Eastern, Open: 348.48, High: 348.48, Low: 348.38, Close: 348.4, Volume: 1400, WAP: 348.408, BarCount: 8\n", "20250527 06:23:0\n", "Date: 20250527 06:23:00 US/Eastern, Open: 348.49, High: 348.5, Low: 348.46, Close: 348.46, Volume: 835, WAP: 348.491, BarCount: 6\n", "20250527 06:24:0\n", "Date: 20250527 06:24:00 US/Eastern, Open: 348.41, High: 348.41, Low: 348.41, Close: 348.41, Volume: 100, WAP: 348.41, BarCount: 1\n", "20250527 06:25:0\n", "Date: 20250527 06:25:00 US/Eastern, Open: 348.49, High: 348.55, Low: 348.49, Close: 348.55, Volume: 488, WAP: 348.536, BarCount: 4\n", "20250527 06:26:0\n", "Date: 20250527 06:26:00 US/Eastern, Open: 348.54, High: 348.65, Low: 348.54, Close: 348.65, Volume: 4749, WAP: 348.627, BarCount: 18\n", "20250527 06:27:0\n", "Date: 20250527 06:27:00 US/Eastern, Open: 348.6, High: 348.6, Low: 348.46, Close: 348.46, Volume: 1035, WAP: 348.486, BarCount: 5\n", "20250527 06:28:0\n", "Date: 20250527 06:28:00 US/Eastern, Open: 348.48, High: 348.48, Low: 348.48, Close: 348.48, Volume: 190, WAP: 348.48, BarCount: 1\n", "20250527 06:29:0\n", "Date: 20250527 06:29:00 US/Eastern, Open: 348.46, High: 348.48, Low: 348.42, Close: 348.48, Volume: 682, WAP: 348.451, BarCount: 5\n", "20250527 06:30:0\n", "Date: 20250527 06:30:00 US/Eastern, Open: 348.5, High: 348.61, Low: 348.2, Close: 348.2, Volume: 5134, WAP: 348.363, BarCount: 31\n", "20250527 06:31:0\n", "Date: 20250527 06:31:00 US/Eastern, Open: 348.2, High: 348.25, Low: 348.15, Close: 348.17, Volume: 2606, WAP: 348.188, BarCount: 25\n", "20250527 06:32:0\n", "Date: 20250527 06:32:00 US/Eastern, Open: 348.26, High: 348.26, Low: 348.26, Close: 348.26, Volume: 200, WAP: 348.26, BarCount: 1\n", "20250527 06:33:0\n", "Date: 20250527 06:33:00 US/Eastern, Open: 348.55, High: 348.58, Low: 348.5, Close: 348.5, Volume: 550, WAP: 348.535, BarCount: 5\n", "20250527 06:34:0\n", "Date: 20250527 06:34:00 US/Eastern, Open: 348.6, High: 348.6, Low: 348.52, Close: 348.52, Volume: 499, WAP: 348.536, BarCount: 4\n", "20250527 06:35:0\n", "Date: 20250527 06:35:00 US/Eastern, Open: 348.6, High: 348.6, Low: 348.6, Close: 348.6, Volume: 134, WAP: 348.6, BarCount: 1\n", "20250527 06:36:0\n", "Date: 20250527 06:36:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.5, Close: 348.5, Volume: 453, WAP: 348.5, BarCount: 3\n", "20250527 06:37:0\n", "Date: 20250527 06:37:00 US/Eastern, Open: 348.41, High: 348.5, Low: 348.38, Close: 348.5, Volume: 2343, WAP: 348.465, BarCount: 14\n", "20250527 06:38:0\n", "Date: 20250527 06:38:00 US/Eastern, Open: 348.45, High: 348.47, Low: 348.45, Close: 348.47, Volume: 458, WAP: 348.458, BarCount: 3\n", "20250527 06:39:0\n", "Date: 20250527 06:39:00 US/Eastern, Open: 348.47, High: 348.47, Low: 348.47, Close: 348.47, Volume: 0, WAP: 348.47, BarCount: 0\n", "20250527 06:40:0\n", "Date: 20250527 06:40:00 US/Eastern, Open: 348.45, High: 348.45, Low: 348.3, Close: 348.31, Volume: 765, WAP: 348.37, BarCount: 6\n", "20250527 06:41:0\n", "Date: 20250527 06:41:00 US/Eastern, Open: 348.5, High: 348.52, Low: 348.39, Close: 348.39, Volume: 1750, WAP: 348.474, BarCount: 16\n", "20250527 06:42:0\n", "Date: 20250527 06:42:00 US/Eastern, Open: 348.47, High: 348.48, Low: 348.33, Close: 348.35, Volume: 750, WAP: 348.4, BarCount: 6\n", "20250527 06:43:0\n", "Date: 20250527 06:43:00 US/Eastern, Open: 348.4, High: 348.54, Low: 348.4, Close: 348.54, Volume: 950, WAP: 348.415, BarCount: 2\n", "20250527 06:44:0\n", "Date: 20250527 06:44:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.5, Close: 348.5, Volume: 101, WAP: 348.5, BarCount: 1\n", "20250527 06:45:0\n", "Date: 20250527 06:45:00 US/Eastern, Open: 348.5, High: 348.5, Low: 348.5, Close: 348.5, Volume: 0, WAP: 348.5, BarCount: 0\n", "20250527 06:46:0\n", "Date: 20250527 06:46:00 US/Eastern, Open: 348.37, High: 348.37, Low: 348.37, Close: 348.37, Volume: 100, WAP: 348.37, BarCount: 1\n", "20250527 06:47:0\n", "Date: 20250527 06:47:00 US/Eastern, Open: 348.28, High: 348.28, Low: 348.28, Close: 348.28, Volume: 150, WAP: 348.28, BarCount: 1\n", "20250527 06:48:0\n", "Date: 20250527 06:48:00 US/Eastern, Open: 348.25, High: 348.26, Low: 348.2, Close: 348.2, Volume: 440, WAP: 348.238, BarCount: 4\n", "20250527 06:49:0\n", "Date: 20250527 06:49:00 US/Eastern, Open: 348.18, High: 348.22, Low: 348.18, Close: 348.22, Volume: 445, WAP: 348.189, BarCount: 3\n", "20250527 06:50:0\n", "Date: 20250527 06:50:00 US/Eastern, Open: 348.22, High: 348.3, Low: 348.2, Close: 348.29, Volume: 1079, WAP: 348.223, BarCount: 9\n", "20250527 06:51:0\n", "Date: 20250527 06:51:00 US/Eastern, Open: 348.31, High: 348.41, Low: 348.31, Close: 348.34, Volume: 800, WAP: 348.366, BarCount: 7\n", "20250527 06:52:0\n", "Date: 20250527 06:52:00 US/Eastern, Open: 348.35, High: 348.39, Low: 348.35, Close: 348.35, Volume: 658, WAP: 348.367, BarCount: 5\n", "20250527 06:53:0\n", "Date: 20250527 06:53:00 US/Eastern, Open: 348.37, High: 348.37, Low: 348.27, Close: 348.27, Volume: 1471, WAP: 348.314, BarCount: 8\n", "20250527 06:54:0\n", "Date: 20250527 06:54:00 US/Eastern, Open: 348.24, High: 348.28, Low: 348.24, Close: 348.25, Volume: 1445, WAP: 348.252, BarCount: 8\n", "20250527 06:55:0\n", "Date: 20250527 06:55:00 US/Eastern, Open: 348.21, High: 348.21, Low: 348.16, Close: 348.16, Volume: 650, WAP: 348.168, BarCount: 5\n", "20250527 06:56:0\n", "Date: 20250527 06:56:00 US/Eastern, Open: 348.17, High: 348.17, Low: 348.02, Close: 348.05, Volume: 2405, WAP: 348.08, BarCount: 17\n", "20250527 06:57:0\n", "Date: 20250527 06:57:00 US/Eastern, Open: 348, High: 348, Low: 347.98, Close: 347.98, Volume: 2615, WAP: 347.995, BarCount: 17\n", "20250527 06:58:0\n", "Date: 20250527 06:58:00 US/Eastern, Open: 348.02, High: 348.17, Low: 347.98, Close: 348.17, Volume: 1907, WAP: 348.035, BarCount: 11\n", "20250527 06:59:0\n", "Date: 20250527 06:59:00 US/Eastern, Open: 348.09, High: 348.09, Low: 347.95, Close: 347.95, Volume: 1045, WAP: 348.004, BarCount: 8\n", "20250527 07:00:0\n", "Date: 20250527 07:00:00 US/Eastern, Open: 347.9, High: 348.15, Low: 347.34, Close: 347.34, Volume: 17014, WAP: 347.686, BarCount: 106\n", "20250527 07:01:0\n", "Date: 20250527 07:01:00 US/Eastern, Open: 347.31, High: 347.38, Low: 346.9, Close: 346.99, Volume: 13577, WAP: 347.074, BarCount: 80\n", "20250527 07:02:0\n", "Date: 20250527 07:02:00 US/Eastern, Open: 347, High: 347.4, Low: 347, Close: 347.4, Volume: 5097, WAP: 347.241, BarCount: 34\n", "20250527 07:03:0\n", "Date: 20250527 07:03:00 US/Eastern, Open: 347.28, High: 347.28, Low: 346.9, Close: 346.93, Volume: 7448, WAP: 347.055, BarCount: 48\n", "20250527 07:04:0\n", "Date: 20250527 07:04:00 US/Eastern, Open: 346.91, High: 347, Low: 346.52, Close: 346.54, Volume: 8791, WAP: 346.795, BarCount: 63\n", "20250527 07:05:0\n", "Date: 20250527 07:05:00 US/Eastern, Open: 346.6, High: 347.01, Low: 346.6, Close: 346.87, Volume: 5214, WAP: 346.892, BarCount: 43\n", "20250527 07:06:0\n", "Date: 20250527 07:06:00 US/Eastern, Open: 346.98, High: 346.98, Low: 346.61, Close: 346.61, Volume: 3718, WAP: 346.831, BarCount: 20\n", "20250527 07:07:0\n", "Date: 20250527 07:07:00 US/Eastern, Open: 346.62, High: 346.62, Low: 346.4, Close: 346.56, Volume: 5686, WAP: 346.535, BarCount: 41\n", "20250527 07:08:0\n", "Date: 20250527 07:08:00 US/Eastern, Open: 346.56, High: 347, Low: 346.55, Close: 346.94, Volume: 6715, WAP: 346.859, BarCount: 44\n", "20250527 07:09:0\n", "Date: 20250527 07:09:00 US/Eastern, Open: 346.99, High: 347.25, Low: 346.99, Close: 347.23, Volume: 9308, WAP: 347.13, BarCount: 51\n", "20250527 07:10:0\n", "Date: 20250527 07:10:00 US/Eastern, Open: 347.28, High: 347.5, Low: 347.28, Close: 347.46, Volume: 6862, WAP: 347.413, BarCount: 44\n", "20250527 07:11:0\n", "Date: 20250527 07:11:00 US/Eastern, Open: 347.38, High: 347.39, Low: 347.15, Close: 347.2, Volume: 6284, WAP: 347.293, BarCount: 41\n", "20250527 07:12:0\n", "Date: 20250527 07:12:00 US/Eastern, Open: 347.3, High: 347.3, Low: 347.1, Close: 347.11, Volume: 1783, WAP: 347.168, BarCount: 12\n", "20250527 07:13:0\n", "Date: 20250527 07:13:00 US/Eastern, Open: 347.17, High: 347.25, Low: 347.16, Close: 347.22, Volume: 4116, WAP: 347.205, BarCount: 30\n", "20250527 07:14:0\n", "Date: 20250527 07:14:00 US/Eastern, Open: 347.2, High: 347.25, Low: 347.15, Close: 347.15, Volume: 2941, WAP: 347.193, BarCount: 24\n", "20250527 07:15:0\n", "Date: 20250527 07:15:00 US/Eastern, Open: 347.13, High: 347.2, Low: 346.95, Close: 346.95, Volume: 3962, WAP: 347.024, BarCount: 27\n", "20250527 07:16:0\n", "Date: 20250527 07:16:00 US/Eastern, Open: 346.94, High: 346.94, Low: 346.48, Close: 346.5, Volume: 6889, WAP: 346.577, BarCount: 44\n", "20250527 07:17:0\n", "Date: 20250527 07:17:00 US/Eastern, Open: 346.6, High: 346.6, Low: 346.27, Close: 346.35, Volume: 6630, WAP: 346.456, BarCount: 43\n", "20250527 07:18:0\n", "Date: 20250527 07:18:00 US/Eastern, Open: 346.37, High: 346.37, Low: 346.2, Close: 346.2, Volume: 4927, WAP: 346.292, BarCount: 34\n", "20250527 07:19:0\n", "Date: 20250527 07:19:00 US/Eastern, Open: 346.25, High: 346.4, Low: 346.14, Close: 346.4, Volume: 2852, WAP: 346.233, BarCount: 21\n", "20250527 07:20:0\n", "Date: 20250527 07:20:00 US/Eastern, Open: 346.5, High: 346.79, Low: 346.48, Close: 346.66, Volume: 1398, WAP: 346.574, BarCount: 13\n", "20250527 07:21:0\n", "Date: 20250527 07:21:00 US/Eastern, Open: 346.75, High: 346.75, Low: 346.6, Close: 346.62, Volume: 2057, WAP: 346.65, BarCount: 16\n", "20250527 07:22:0\n", "Date: 20250527 07:22:00 US/Eastern, Open: 346.62, High: 346.62, Low: 346.6, Close: 346.6, Volume: 1540, WAP: 346.603, BarCount: 6\n", "20250527 07:23:0\n", "Date: 20250527 07:23:00 US/Eastern, Open: 346.62, High: 346.62, Low: 346.48, Close: 346.57, Volume: 1619, WAP: 346.541, BarCount: 12\n", "20250527 07:24:0\n", "Date: 20250527 07:24:00 US/Eastern, Open: 346.5, High: 346.54, Low: 346.39, Close: 346.39, Volume: 2305, WAP: 346.492, BarCount: 17\n", "20250527 07:25:0\n", "Date: 20250527 07:25:00 US/Eastern, Open: 346.45, High: 346.7, Low: 346.45, Close: 346.69, Volume: 3135, WAP: 346.533, BarCount: 20\n", "20250527 07:26:0\n", "Date: 20250527 07:26:00 US/Eastern, Open: 346.77, High: 347, Low: 346.77, Close: 346.88, Volume: 8093, WAP: 346.93, BarCount: 36\n", "20250527 07:27:0\n", "Date: 20250527 07:27:00 US/Eastern, Open: 346.91, High: 346.97, Low: 346.88, Close: 346.89, Volume: 1342, WAP: 346.908, BarCount: 10\n", "20250527 07:28:0\n", "Date: 20250527 07:28:00 US/Eastern, Open: 346.89, High: 346.97, Low: 346.82, Close: 346.83, Volume: 3079, WAP: 346.886, BarCount: 20\n", "20250527 07:29:0\n", "Date: 20250527 07:29:00 US/Eastern, Open: 346.82, High: 346.85, Low: 346.78, Close: 346.78, Volume: 806, WAP: 346.797, BarCount: 8\n", "20250527 07:30:0\n", "Date: 20250527 07:30:00 US/Eastern, Open: 346.75, High: 346.84, Low: 346.73, Close: 346.84, Volume: 1895, WAP: 346.784, BarCount: 13\n", "20250527 07:31:0\n", "Date: 20250527 07:31:00 US/Eastern, Open: 346.9, High: 347, Low: 346.84, Close: 347, Volume: 2769, WAP: 346.949, BarCount: 17\n", "20250527 07:32:0\n", "Date: 20250527 07:32:00 US/Eastern, Open: 346.99, High: 347.2, Low: 346.97, Close: 347.2, Volume: 8707, WAP: 347.077, BarCount: 51\n", "20250527 07:33:0\n", "Date: 20250527 07:33:00 US/Eastern, Open: 347.21, High: 347.45, Low: 347.2, Close: 347.4, Volume: 6818, WAP: 347.301, BarCount: 41\n", "20250527 07:34:0\n", "Date: 20250527 07:34:00 US/Eastern, Open: 347.35, High: 347.5, Low: 347.25, Close: 347.35, Volume: 4694, WAP: 347.407, BarCount: 27\n", "20250527 07:35:0\n", "Date: 20250527 07:35:00 US/Eastern, Open: 347.35, High: 347.4, Low: 347.28, Close: 347.33, Volume: 2669, WAP: 347.329, BarCount: 15\n", "20250527 07:36:0\n", "Date: 20250527 07:36:00 US/Eastern, Open: 347.29, High: 347.5, Low: 347.29, Close: 347.4, Volume: 3652, WAP: 347.416, BarCount: 17\n", "20250527 07:37:0\n", "Date: 20250527 07:37:00 US/Eastern, Open: 347.35, High: 347.36, Low: 347.3, Close: 347.32, Volume: 6150, WAP: 347.311, BarCount: 13\n", "20250527 07:38:0\n", "Date: 20250527 07:38:00 US/Eastern, Open: 347.39, High: 347.47, Low: 347.39, Close: 347.4, Volume: 1704, WAP: 347.437, BarCount: 11\n", "20250527 07:39:0\n", "Date: 20250527 07:39:00 US/Eastern, Open: 347.47, High: 347.49, Low: 347.34, Close: 347.34, Volume: 2129, WAP: 347.41, BarCount: 12\n", "20250527 07:40:0\n", "Date: 20250527 07:40:00 US/Eastern, Open: 347.32, High: 347.32, Low: 347.2, Close: 347.2, Volume: 2377, WAP: 347.245, BarCount: 15\n", "20250527 07:41:0\n", "Date: 20250527 07:41:00 US/Eastern, Open: 347.12, High: 347.25, Low: 347.12, Close: 347.25, Volume: 1009, WAP: 347.178, BarCount: 7\n", "20250527 07:42:0\n", "Date: 20250527 07:42:00 US/Eastern, Open: 347.41, High: 347.41, Low: 347.34, Close: 347.4, Volume: 907, WAP: 347.393, BarCount: 8\n", "20250527 07:43:0\n", "Date: 20250527 07:43:00 US/Eastern, Open: 347.45, High: 347.45, Low: 347.4, Close: 347.4, Volume: 1496, WAP: 347.43, BarCount: 13\n", "20250527 07:44:0\n", "Date: 20250527 07:44:00 US/Eastern, Open: 347.36, High: 347.37, Low: 347.34, Close: 347.34, Volume: 543, WAP: 347.351, BarCount: 4\n", "20250527 07:45:0\n", "Date: 20250527 07:45:00 US/Eastern, Open: 347.35, High: 347.41, Low: 347.35, Close: 347.35, Volume: 1087, WAP: 347.375, BarCount: 10\n", "20250527 07:46:0\n", "Date: 20250527 07:46:00 US/Eastern, Open: 347.35, High: 347.49, Low: 347.35, Close: 347.49, Volume: 1476, WAP: 347.429, BarCount: 11\n", "20250527 07:47:0\n", "Date: 20250527 07:47:00 US/Eastern, Open: 347.5, High: 347.69, Low: 347.5, Close: 347.6, Volume: 2535, WAP: 347.566, BarCount: 15\n", "20250527 07:48:0\n", "Date: 20250527 07:48:00 US/Eastern, Open: 347.63, High: 347.76, Low: 347.63, Close: 347.69, Volume: 3361, WAP: 347.728, BarCount: 21\n", "20250527 07:49:0\n", "Date: 20250527 07:49:00 US/Eastern, Open: 347.73, High: 347.86, Low: 347.69, Close: 347.69, Volume: 12161, WAP: 347.813, BarCount: 39\n", "20250527 07:50:0\n", "Date: 20250527 07:50:00 US/Eastern, Open: 347.68, High: 347.68, Low: 347.47, Close: 347.58, Volume: 2178, WAP: 347.568, BarCount: 18\n", "20250527 07:51:0\n", "Date: 20250527 07:51:00 US/Eastern, Open: 347.69, High: 347.74, Low: 347.64, Close: 347.65, Volume: 2411, WAP: 347.662, BarCount: 12\n", "20250527 07:52:0\n", "Date: 20250527 07:52:00 US/Eastern, Open: 347.54, High: 347.54, Low: 347.36, Close: 347.4, Volume: 1868, WAP: 347.433, BarCount: 13\n", "20250527 07:53:0\n", "Date: 20250527 07:53:00 US/Eastern, Open: 347.43, High: 347.44, Low: 347.3, Close: 347.3, Volume: 835, WAP: 347.363, BarCount: 6\n", "20250527 07:54:0\n", "Date: 20250527 07:54:00 US/Eastern, Open: 347.33, High: 347.4, Low: 347.33, Close: 347.35, Volume: 1543, WAP: 347.356, BarCount: 10\n", "20250527 07:55:0\n", "Date: 20250527 07:55:00 US/Eastern, Open: 347.4, High: 347.41, Low: 347.24, Close: 347.24, Volume: 3508, WAP: 347.342, BarCount: 13\n", "20250527 07:56:0\n", "Date: 20250527 07:56:00 US/Eastern, Open: 347.25, High: 347.3, Low: 347.19, Close: 347.3, Volume: 3917, WAP: 347.252, BarCount: 22\n", "20250527 07:57:0\n", "Date: 20250527 07:57:00 US/Eastern, Open: 347.25, High: 347.25, Low: 347.2, Close: 347.2, Volume: 427, WAP: 347.214, BarCount: 3\n", "20250527 07:58:0\n", "Date: 20250527 07:58:00 US/Eastern, Open: 347.21, High: 347.21, Low: 346.95, Close: 347.01, Volume: 5380, WAP: 347.016, BarCount: 35\n", "20250527 07:59:0\n", "Date: 20250527 07:59:00 US/Eastern, Open: 347.04, High: 347.2, Low: 347.04, Close: 347.07, Volume: 1856, WAP: 347.083, BarCount: 13\n", "20250527 08:00:0\n", "Date: 20250527 08:00:00 US/Eastern, Open: 347.5, High: 349.79, Low: 345.66, Close: 346.41, Volume: 314735, WAP: 346.863, BarCount: 1150\n", "20250527 08:01:0\n", "Date: 20250527 08:01:00 US/Eastern, Open: 346.41, High: 347.5, Low: 345.78, Close: 347.35, Volume: 63671, WAP: 346.946, BarCount: 352\n", "20250527 08:02:0\n", "Date: 20250527 08:02:00 US/Eastern, Open: 347.35, High: 347.4, Low: 347, Close: 347.29, Volume: 12038, WAP: 347.235, BarCount: 47\n", "20250527 08:03:0\n", "Date: 20250527 08:03:00 US/Eastern, Open: 347.31, High: 347.31, Low: 347, Close: 347, Volume: 2102, WAP: 347.18, BarCount: 9\n", "20250527 08:04:0\n", "Date: 20250527 08:04:00 US/Eastern, Open: 347, High: 347.08, Low: 346.91, Close: 347.08, Volume: 5664, WAP: 347.006, BarCount: 28\n", "20250527 08:05:0\n", "Date: 20250527 08:05:00 US/Eastern, Open: 347.09, High: 347.1, Low: 346.9, Close: 346.9, Volume: 5623, WAP: 347.023, BarCount: 29\n", "20250527 08:06:0\n", "Date: 20250527 08:06:00 US/Eastern, Open: 346.9, High: 347.09, Low: 346.85, Close: 347.09, Volume: 6059, WAP: 346.941, BarCount: 21\n", "20250527 08:07:0\n", "Date: 20250527 08:07:00 US/Eastern, Open: 347.09, High: 347.1, Low: 346.92, Close: 347.03, Volume: 4288, WAP: 347.033, BarCount: 23\n", "20250527 08:08:0\n", "Date: 20250527 08:08:00 US/Eastern, Open: 346.86, High: 347, Low: 346.79, Close: 346.99, Volume: 4396, WAP: 346.885, BarCount: 29\n", "20250527 08:09:0\n", "Date: 20250527 08:09:00 US/Eastern, Open: 347, High: 347.14, Low: 347, Close: 347.03, Volume: 3316, WAP: 347.089, BarCount: 10\n", "20250527 08:10:0\n", "Date: 20250527 08:10:00 US/Eastern, Open: 347, High: 347.05, Low: 346.96, Close: 347.05, Volume: 1022, WAP: 347.003, BarCount: 6\n", "20250527 08:11:0\n", "Date: 20250527 08:11:00 US/Eastern, Open: 347.05, High: 347.15, Low: 347, Close: 347.07, Volume: 2755, WAP: 347.084, BarCount: 16\n", "20250527 08:12:0\n", "Date: 20250527 08:12:00 US/Eastern, Open: 347.02, High: 347.1, Low: 347.02, Close: 347.1, Volume: 907, WAP: 347.079, BarCount: 4\n", "20250527 08:13:0\n", "Date: 20250527 08:13:00 US/Eastern, Open: 347.04, High: 347.1, Low: 346.96, Close: 347, Volume: 2141, WAP: 347.021, BarCount: 13\n", "20250527 08:14:0\n", "Date: 20250527 08:14:00 US/Eastern, Open: 347, High: 347.1, Low: 346.83, Close: 346.83, Volume: 4682, WAP: 346.923, BarCount: 30\n", "20250527 08:15:0\n", "Date: 20250527 08:15:00 US/Eastern, Open: 346.81, High: 347.08, Low: 346.81, Close: 347, Volume: 2212, WAP: 346.955, BarCount: 16\n", "20250527 08:16:0\n", "Date: 20250527 08:16:00 US/Eastern, Open: 346.94, High: 346.98, Low: 346.83, Close: 346.87, Volume: 3236, WAP: 346.891, BarCount: 19\n", "20250527 08:17:0\n", "Date: 20250527 08:17:00 US/Eastern, Open: 346.95, High: 346.95, Low: 346.7, Close: 346.9, Volume: 4361, WAP: 346.826, BarCount: 27\n", "20250527 08:18:0\n", "Date: 20250527 08:18:00 US/Eastern, Open: 346.9, High: 347, Low: 346.8, Close: 347, Volume: 4152, WAP: 346.914, BarCount: 20\n", "20250527 08:19:0\n", "Date: 20250527 08:19:00 US/Eastern, Open: 346.97, High: 347.02, Low: 346.8, Close: 346.82, Volume: 3087, WAP: 346.9, BarCount: 20\n", "20250527 08:20:0\n", "Date: 20250527 08:20:00 US/Eastern, Open: 346.89, High: 347.28, Low: 346.8, Close: 347.28, Volume: 7205, WAP: 347.086, BarCount: 39\n", "20250527 08:21:0\n", "Date: 20250527 08:21:00 US/Eastern, Open: 347.3, High: 347.39, Low: 347.2, Close: 347.39, Volume: 8300, WAP: 347.321, BarCount: 29\n", "20250527 08:22:0\n", "Date: 20250527 08:22:00 US/Eastern, Open: 347.3, High: 347.4, Low: 347.22, Close: 347.22, Volume: 5665, WAP: 347.306, BarCount: 33\n", "20250527 08:23:0\n", "Date: 20250527 08:23:00 US/Eastern, Open: 347.2, High: 347.3, Low: 347.08, Close: 347.11, Volume: 1849, WAP: 347.213, BarCount: 13\n", "20250527 08:24:0\n", "Date: 20250527 08:24:00 US/Eastern, Open: 347.09, High: 347.27, Low: 347.09, Close: 347.2, Volume: 2900, WAP: 347.194, BarCount: 13\n", "20250527 08:25:0\n", "Date: 20250527 08:25:00 US/Eastern, Open: 347.18, High: 347.2, Low: 346.95, Close: 346.95, Volume: 4395, WAP: 347.09, BarCount: 29\n", "20250527 08:26:0\n", "Date: 20250527 08:26:00 US/Eastern, Open: 347.05, High: 347.15, Low: 347, Close: 347.1, Volume: 3653, WAP: 347.097, BarCount: 20\n", "20250527 08:27:0\n", "Date: 20250527 08:27:00 US/Eastern, Open: 347.09, High: 347.2, Low: 347.05, Close: 347.15, Volume: 2044, WAP: 347.143, BarCount: 16\n", "20250527 08:28:0\n", "Date: 20250527 08:28:00 US/Eastern, Open: 347.11, High: 347.21, Low: 347.05, Close: 347.2, Volume: 4881, WAP: 347.136, BarCount: 19\n", "20250527 08:29:0\n", "Date: 20250527 08:29:00 US/Eastern, Open: 347.27, High: 347.38, Low: 347.2, Close: 347.38, Volume: 6495, WAP: 347.244, BarCount: 32\n", "20250527 08:30:0\n", "Date: 20250527 08:30:00 US/Eastern, Open: 347.3, High: 347.51, Low: 347.22, Close: 347.3, Volume: 12977, WAP: 347.406, BarCount: 57\n", "20250527 08:31:0\n", "Date: 20250527 08:31:00 US/Eastern, Open: 347.3, High: 347.5, Low: 347.3, Close: 347.5, Volume: 6186, WAP: 347.445, BarCount: 30\n", "20250527 08:32:0\n", "Date: 20250527 08:32:00 US/Eastern, Open: 347.49, High: 347.55, Low: 347.34, Close: 347.34, Volume: 7613, WAP: 347.458, BarCount: 34\n", "20250527 08:33:0\n", "Date: 20250527 08:33:00 US/Eastern, Open: 347.4, High: 347.4, Low: 347.28, Close: 347.3, Volume: 2890, WAP: 347.324, BarCount: 13\n", "20250527 08:34:0\n", "Date: 20250527 08:34:00 US/Eastern, Open: 347.25, High: 347.37, Low: 347.25, Close: 347.37, Volume: 2103, WAP: 347.331, BarCount: 9\n", "20250527 08:35:0\n", "Date: 20250527 08:35:00 US/Eastern, Open: 347.46, High: 347.46, Low: 347.3, Close: 347.3, Volume: 2904, WAP: 347.438, BarCount: 4\n", "20250527 08:36:0\n", "Date: 20250527 08:36:00 US/Eastern, Open: 347.35, High: 347.45, Low: 347.31, Close: 347.32, Volume: 4041, WAP: 347.35, BarCount: 26\n", "20250527 08:37:0\n", "Date: 20250527 08:37:00 US/Eastern, Open: 347.35, High: 347.46, Low: 347.27, Close: 347.27, Volume: 2867, WAP: 347.332, BarCount: 18\n", "20250527 08:38:0\n", "Date: 20250527 08:38:00 US/Eastern, Open: 347.35, High: 347.35, Low: 347.11, Close: 347.12, Volume: 4027, WAP: 347.215, BarCount: 18\n", "20250527 08:39:0\n", "Date: 20250527 08:39:00 US/Eastern, Open: 347.16, High: 347.23, Low: 347.05, Close: 347.12, Volume: 6559, WAP: 347.141, BarCount: 45\n", "20250527 08:40:0\n", "Date: 20250527 08:40:00 US/Eastern, Open: 347.11, High: 347.2, Low: 347.02, Close: 347.1, Volume: 6585, WAP: 347.076, BarCount: 24\n", "20250527 08:41:0\n", "Date: 20250527 08:41:00 US/Eastern, Open: 347.03, High: 347.03, Low: 346.92, Close: 347, Volume: 10678, WAP: 346.998, BarCount: 37\n", "20250527 08:42:0\n", "Date: 20250527 08:42:00 US/Eastern, Open: 347, High: 347.05, Low: 346.86, Close: 347.05, Volume: 4882, WAP: 346.942, BarCount: 30\n", "20250527 08:43:0\n", "Date: 20250527 08:43:00 US/Eastern, Open: 347.07, High: 347.11, Low: 347, Close: 347.09, Volume: 2893, WAP: 347.034, BarCount: 21\n", "20250527 08:44:0\n", "Date: 20250527 08:44:00 US/Eastern, Open: 347.05, High: 347.07, Low: 346.92, Close: 346.96, Volume: 3330, WAP: 347.024, BarCount: 18\n", "20250527 08:45:0\n", "Date: 20250527 08:45:00 US/Eastern, Open: 347, High: 347.07, Low: 347, Close: 347, Volume: 2744, WAP: 347.029, BarCount: 14\n", "20250527 08:46:0\n", "Date: 20250527 08:46:00 US/Eastern, Open: 347, High: 347.05, Low: 347, Close: 347, Volume: 2008, WAP: 347.038, BarCount: 8\n", "20250527 08:47:0\n", "Date: 20250527 08:47:00 US/Eastern, Open: 347, High: 347.1, Low: 346.99, Close: 347.02, Volume: 2678, WAP: 347.014, BarCount: 9\n", "20250527 08:48:0\n", "Date: 20250527 08:48:00 US/Eastern, Open: 347.1, High: 347.2, Low: 347, Close: 347.13, Volume: 3575, WAP: 347.072, BarCount: 22\n", "20250527 08:49:0\n", "Date: 20250527 08:49:00 US/Eastern, Open: 347.1, High: 347.25, Low: 347.1, Close: 347.2, Volume: 3067, WAP: 347.183, BarCount: 19\n", "20250527 08:50:0\n", "Date: 20250527 08:50:00 US/Eastern, Open: 347.25, High: 347.25, Low: 346.97, Close: 346.97, Volume: 3332, WAP: 347.147, BarCount: 17\n", "20250527 08:51:0\n", "Date: 20250527 08:51:00 US/Eastern, Open: 347, High: 347.19, Low: 347, Close: 347.17, Volume: 2034, WAP: 347.108, BarCount: 13\n", "20250527 08:52:0\n", "Date: 20250527 08:52:00 US/Eastern, Open: 347.05, High: 347.06, Low: 346.88, Close: 346.98, Volume: 5439, WAP: 346.923, BarCount: 27\n", "20250527 08:53:0\n", "Date: 20250527 08:53:00 US/Eastern, Open: 346.9, High: 346.94, Low: 346.88, Close: 346.89, Volume: 1130, WAP: 346.902, BarCount: 9\n", "20250527 08:54:0\n", "Date: 20250527 08:54:00 US/Eastern, Open: 346.87, High: 346.94, Low: 346.83, Close: 346.83, Volume: 4566, WAP: 346.883, BarCount: 25\n", "20250527 08:55:0\n", "Date: 20250527 08:55:00 US/Eastern, Open: 346.83, High: 346.85, Low: 346.74, Close: 346.76, Volume: 7199, WAP: 346.788, BarCount: 36\n", "20250527 08:56:0\n", "Date: 20250527 08:56:00 US/Eastern, Open: 346.85, High: 347.14, Low: 346.85, Close: 347.1, Volume: 9020, WAP: 346.982, BarCount: 35\n", "20250527 08:57:0\n", "Date: 20250527 08:57:00 US/Eastern, Open: 347.12, High: 347.19, Low: 347.07, Close: 347.1, Volume: 6031, WAP: 347.132, BarCount: 36\n", "20250527 08:58:0\n", "Date: 20250527 08:58:00 US/Eastern, Open: 347.11, High: 347.2, Low: 347.07, Close: 347.11, Volume: 5630, WAP: 347.166, BarCount: 18\n", "20250527 08:59:0\n", "Date: 20250527 08:59:00 US/Eastern, Open: 347.2, High: 347.23, Low: 347.05, Close: 347.23, Volume: 6048, WAP: 347.159, BarCount: 33\n", "20250527 09:00:0\n", "Date: 20250527 09:00:00 US/Eastern, Open: 347.23, High: 347.29, Low: 347.16, Close: 347.19, Volume: 9183, WAP: 347.217, BarCount: 32\n", "20250527 09:01:0\n", "Date: 20250527 09:01:00 US/Eastern, Open: 347.18, High: 347.28, Low: 347.16, Close: 347.23, Volume: 5947, WAP: 347.198, BarCount: 23\n", "20250527 09:02:0\n", "Date: 20250527 09:02:00 US/Eastern, Open: 347.25, High: 347.3, Low: 347.13, Close: 347.13, Volume: 4963, WAP: 347.222, BarCount: 16\n", "20250527 09:03:0\n", "Date: 20250527 09:03:00 US/Eastern, Open: 347.08, High: 347.25, Low: 347.08, Close: 347.25, Volume: 3217, WAP: 347.178, BarCount: 14\n", "20250527 09:04:0\n", "Date: 20250527 09:04:00 US/Eastern, Open: 347.24, High: 347.41, Low: 347.23, Close: 347.41, Volume: 4202, WAP: 347.304, BarCount: 28\n", "20250527 09:05:0\n", "Date: 20250527 09:05:00 US/Eastern, Open: 347.4, High: 347.55, Low: 347.4, Close: 347.55, Volume: 8736, WAP: 347.474, BarCount: 35\n", "20250527 09:06:0\n", "Date: 20250527 09:06:00 US/Eastern, Open: 347.58, High: 347.58, Low: 347.45, Close: 347.52, Volume: 6493, WAP: 347.523, BarCount: 30\n", "20250527 09:07:0\n", "Date: 20250527 09:07:00 US/Eastern, Open: 347.55, High: 347.77, Low: 347.47, Close: 347.71, Volume: 14189, WAP: 347.658, BarCount: 56\n", "20250527 09:08:0\n", "Date: 20250527 09:08:00 US/Eastern, Open: 347.61, High: 348.25, Low: 347.61, Close: 348.09, Volume: 47760, WAP: 347.924, BarCount: 169\n", "20250527 09:09:0\n", "Date: 20250527 09:09:00 US/Eastern, Open: 348.06, High: 348.11, Low: 347.85, Close: 347.85, Volume: 11884, WAP: 348.003, BarCount: 56\n", "20250527 09:10:0\n", "Date: 20250527 09:10:00 US/Eastern, Open: 347.89, High: 348, Low: 347.75, Close: 347.8, Volume: 5375, WAP: 347.914, BarCount: 27\n", "20250527 09:11:0\n", "Date: 20250527 09:11:00 US/Eastern, Open: 347.85, High: 347.93, Low: 347.75, Close: 347.8, Volume: 7131, WAP: 347.836, BarCount: 34\n", "20250527 09:12:0\n", "Date: 20250527 09:12:00 US/Eastern, Open: 347.8, High: 347.85, Low: 347.66, Close: 347.7, Volume: 5586, WAP: 347.781, BarCount: 18\n", "20250527 09:13:0\n", "Date: 20250527 09:13:00 US/Eastern, Open: 347.65, High: 347.7, Low: 347.5, Close: 347.59, Volume: 8815, WAP: 347.559, BarCount: 39\n", "20250527 09:14:0\n", "Date: 20250527 09:14:00 US/Eastern, Open: 347.59, High: 347.65, Low: 347.5, Close: 347.62, Volume: 5044, WAP: 347.568, BarCount: 22\n", "20250527 09:15:0\n", "Date: 20250527 09:15:00 US/Eastern, Open: 347.7, High: 347.93, Low: 347.7, Close: 347.89, Volume: 13489, WAP: 347.859, BarCount: 49\n", "20250527 09:16:0\n", "Date: 20250527 09:16:00 US/Eastern, Open: 347.88, High: 348.13, Low: 347.88, Close: 348.1, Volume: 13162, WAP: 348.003, BarCount: 66\n", "20250527 09:17:0\n", "Date: 20250527 09:17:00 US/Eastern, Open: 348.03, High: 348.38, Low: 348.03, Close: 348.3, Volume: 18669, WAP: 348.226, BarCount: 72\n", "20250527 09:18:0\n", "Date: 20250527 09:18:00 US/Eastern, Open: 348.3, High: 348.3, Low: 348.07, Close: 348.12, Volume: 11747, WAP: 348.199, BarCount: 55\n", "20250527 09:19:0\n", "Date: 20250527 09:19:00 US/Eastern, Open: 348.2, High: 348.2, Low: 348.01, Close: 348.12, Volume: 5637, WAP: 348.113, BarCount: 31\n", "20250527 09:20:0\n", "Date: 20250527 09:20:00 US/Eastern, Open: 348.1, High: 348.35, Low: 348.05, Close: 348.28, Volume: 11459, WAP: 348.213, BarCount: 55\n", "20250527 09:21:0\n", "Date: 20250527 09:21:00 US/Eastern, Open: 348.2, High: 348.29, Low: 348.13, Close: 348.15, Volume: 13535, WAP: 348.215, BarCount: 54\n", "20250527 09:22:0\n", "Date: 20250527 09:22:00 US/Eastern, Open: 348.15, High: 348.15, Low: 347.8, Close: 347.8, Volume: 13232, WAP: 347.96, BarCount: 62\n", "20250527 09:23:0\n", "Date: 20250527 09:23:00 US/Eastern, Open: 347.75, High: 347.93, Low: 347.73, Close: 347.93, Volume: 6329, WAP: 347.847, BarCount: 39\n", "20250527 09:24:0\n", "Date: 20250527 09:24:00 US/Eastern, Open: 347.94, High: 347.98, Low: 347.8, Close: 347.98, Volume: 5252, WAP: 347.895, BarCount: 22\n", "20250527 09:25:0\n", "Date: 20250527 09:25:00 US/Eastern, Open: 347.85, High: 348.04, Low: 347.61, Close: 347.92, Volume: 10697, WAP: 347.883, BarCount: 54\n", "20250527 09:26:0\n", "Date: 20250527 09:26:00 US/Eastern, Open: 348, High: 348.2, Low: 347.78, Close: 347.91, Volume: 12226, WAP: 347.948, BarCount: 72\n", "20250527 09:27:0\n", "Date: 20250527 09:27:00 US/Eastern, Open: 347.93, High: 348.1, Low: 347.83, Close: 348, Volume: 13699, WAP: 348.027, BarCount: 74\n", "20250527 09:28:0\n", "Date: 20250527 09:28:00 US/Eastern, Open: 347.9, High: 347.9, Low: 346.7, Close: 347.55, Volume: 63808, WAP: 347.237, BarCount: 310\n", "20250527 09:29:0\n", "Date: 20250527 09:29:00 US/Eastern, Open: 347.63, High: 347.75, Low: 347.2, Close: 347.29, Volume: 22461, WAP: 347.311, BarCount: 116\n", "20250527 09:30:0\n", "Date: 20250527 09:30:00 US/Eastern, Open: 347.27, High: 351.65, Low: 347.27, Close: 350.97, Volume: 1719540, WAP: 349.29, BarCount: 5871\n", "20250527 09:31:0\n", "Date: 20250527 09:31:00 US/Eastern, Open: 350.88, High: 351.77, Low: 350.15, Close: 350.43, Volume: 761537, WAP: 350.902, BarCount: 3884\n", "20250527 09:32:0\n", "Date: 20250527 09:32:00 US/Eastern, Open: 350.46, High: 352.59, Low: 350.34, Close: 352.2, Volume: 742533, WAP: 351.836, BarCount: 3491\n", "20250527 09:33:0\n", "Date: 20250527 09:33:00 US/Eastern, Open: 352.29, High: 353.44, Low: 352.04, Close: 353.07, Volume: 764844, WAP: 352.728, BarCount: 3493\n", "20250527 09:34:0\n", "Date: 20250527 09:34:00 US/Eastern, Open: 353.01, High: 353.44, Low: 351.67, Close: 352.26, Volume: 585332, WAP: 352.655, BarCount: 2926\n", "20250527 09:35:0\n", "Date: 20250527 09:35:00 US/Eastern, Open: 352.14, High: 352.4, Low: 350.52, Close: 350.68, Volume: 566814, WAP: 351.501, BarCount: 2856\n", "20250527 09:36:0\n", "Date: 20250527 09:36:00 US/Eastern, Open: 350.63, High: 351.46, Low: 349.99, Close: 350.81, Volume: 573552, WAP: 350.595, BarCount: 2499\n", "20250527 09:37:0\n", "Date: 20250527 09:37:00 US/Eastern, Open: 350.8, High: 351.19, Low: 349.8, Close: 350.48, Volume: 510859, WAP: 350.469, BarCount: 2556\n", "20250527 09:38:0\n", "Date: 20250527 09:38:00 US/Eastern, Open: 350.47, High: 352.79, Low: 350.4, Close: 352.79, Volume: 666603, WAP: 352.031, BarCount: 3089\n", "20250527 09:39:0\n", "Date: 20250527 09:39:00 US/Eastern, Open: 352.77, High: 353.74, Low: 352.32, Close: 353.69, Volume: 842898, WAP: 353.223, BarCount: 3929\n", "20250527 09:40:0\n", "Date: 20250527 09:40:00 US/Eastern, Open: 353.6, High: 354.55, Low: 352.43, Close: 352.72, Volume: 897577, WAP: 353.609, BarCount: 4336\n", "20250527 09:41:0\n", "Date: 20250527 09:41:00 US/Eastern, Open: 352.7, High: 353.33, Low: 351.8, Close: 352.2, Volume: 495991, WAP: 352.238, BarCount: 2377\n", "20250527 09:42:0\n", "Date: 20250527 09:42:00 US/Eastern, Open: 352.18, High: 352.33, Low: 350.93, Close: 351.23, Volume: 343011, WAP: 351.545, BarCount: 1653\n", "20250527 09:43:0\n", "Date: 20250527 09:43:00 US/Eastern, Open: 351.18, High: 352.1, Low: 350.66, Close: 351.68, Volume: 340625, WAP: 351.469, BarCount: 1729\n", "20250527 09:44:0\n", "Date: 20250527 09:44:00 US/Eastern, Open: 351.68, High: 353.25, Low: 351.49, Close: 352.85, Volume: 393176, WAP: 352.459, BarCount: 2000\n", "20250527 09:45:0\n", "Date: 20250527 09:45:00 US/Eastern, Open: 352.94, High: 353.5, Low: 351.42, Close: 352.29, Volume: 391539, WAP: 352.51, BarCount: 1807\n", "20250527 09:46:0\n", "Date: 20250527 09:46:00 US/Eastern, Open: 352.21, High: 353.39, Low: 351.85, Close: 353.08, Volume: 383992, WAP: 352.583, BarCount: 1907\n", "20250527 09:47:0\n", "Date: 20250527 09:47:00 US/Eastern, Open: 353.08, High: 353.73, Low: 352.45, Close: 352.52, Volume: 468817, WAP: 353.224, BarCount: 2218\n", "20250527 09:48:0\n", "Date: 20250527 09:48:00 US/Eastern, Open: 352.59, High: 353.19, Low: 352.27, Close: 353.07, Volume: 237919, WAP: 352.717, BarCount: 1231\n", "20250527 09:49:0\n", "Date: 20250527 09:49:00 US/Eastern, Open: 353.04, High: 353.4, Low: 352.69, Close: 352.77, Volume: 254074, WAP: 353.033, BarCount: 1275\n", "20250527 09:50:0\n", "Date: 20250527 09:50:00 US/Eastern, Open: 352.8, High: 352.99, Low: 351.4, Close: 352.11, Volume: 363235, WAP: 352.003, BarCount: 1732\n", "20250527 09:51:0\n", "Date: 20250527 09:51:00 US/Eastern, Open: 352.2, High: 352.21, Low: 351.36, Close: 352.11, Volume: 216887, WAP: 351.727, BarCount: 1137\n", "20250527 09:52:0\n", "Date: 20250527 09:52:00 US/Eastern, Open: 352.1, High: 352.69, Low: 351.95, Close: 352.33, Volume: 216089, WAP: 352.33, BarCount: 1156\n", "20250527 09:53:0\n", "Date: 20250527 09:53:00 US/Eastern, Open: 352.33, High: 352.39, Low: 351.56, Close: 352.08, Volume: 211339, WAP: 351.93, BarCount: 1098\n", "20250527 09:54:0\n", "Date: 20250527 09:54:00 US/Eastern, Open: 352.13, High: 352.17, Low: 351.4, Close: 352.12, Volume: 269383, WAP: 351.747, BarCount: 1288\n", "20250527 09:55:0\n", "Date: 20250527 09:55:00 US/Eastern, Open: 352.13, High: 352.88, Low: 351.75, Close: 351.89, Volume: 319631, WAP: 352.387, BarCount: 1646\n", "20250527 09:56:0\n", "Date: 20250527 09:56:00 US/Eastern, Open: 351.94, High: 352.11, Low: 351.63, Close: 352, Volume: 192885, WAP: 351.903, BarCount: 967\n", "20250527 09:57:0\n", "Date: 20250527 09:57:00 US/Eastern, Open: 351.94, High: 352.97, Low: 351.8, Close: 352.72, Volume: 254436, WAP: 352.458, BarCount: 1187\n", "20250527 09:58:0\n", "Date: 20250527 09:58:00 US/Eastern, Open: 352.7, High: 353.93, Low: 352.6, Close: 353.9, Volume: 556635, WAP: 353.362, BarCount: 2733\n", "20250527 09:59:0\n", "Date: 20250527 09:59:00 US/Eastern, Open: 353.91, High: 354.39, Low: 353.07, Close: 353.16, Volume: 510606, WAP: 353.791, BarCount: 2396\n", "20250527 10:00:0\n", "Date: 20250527 10:00:00 US/Eastern, Open: 353.22, High: 355, Low: 352.93, Close: 354.31, Volume: 629202, WAP: 354.046, BarCount: 2806\n", "20250527 10:01:0\n", "Date: 20250527 10:01:00 US/Eastern, Open: 354.19, High: 354.62, Low: 353.6, Close: 354.44, Volume: 430543, WAP: 354.124, BarCount: 1952\n", "20250527 10:02:0\n", "Date: 20250527 10:02:00 US/Eastern, Open: 354.4, High: 355.74, Low: 354.15, Close: 355.46, Volume: 951885, WAP: 355.089, BarCount: 4259\n", "20250527 10:03:0\n", "Date: 20250527 10:03:00 US/Eastern, Open: 355.47, High: 355.74, Low: 354.66, Close: 354.73, Volume: 435195, WAP: 355.227, BarCount: 2170\n", "20250527 10:04:0\n", "Date: 20250527 10:04:00 US/Eastern, Open: 354.73, High: 355.75, Low: 354.68, Close: 355.27, Volume: 467356, WAP: 355.282, BarCount: 2259\n", "20250527 10:05:0\n", "Date: 20250527 10:05:00 US/Eastern, Open: 355.27, High: 357.11, Low: 355.26, Close: 356.02, Volume: 786187, WAP: 356.318, BarCount: 3772\n", "20250527 10:06:0\n", "Date: 20250527 10:06:00 US/Eastern, Open: 355.98, High: 356.89, Low: 355.81, Close: 356.71, Volume: 420676, WAP: 356.356, BarCount: 1967\n", "20250527 10:07:0\n", "Date: 20250527 10:07:00 US/Eastern, Open: 356.74, High: 357, Low: 356.1, Close: 356.31, Volume: 311472, WAP: 356.568, BarCount: 1541\n", "20250527 10:08:0\n", "Date: 20250527 10:08:00 US/Eastern, Open: 356.29, High: 356.3, Low: 355.31, Close: 355.31, Volume: 413257, WAP: 355.64, BarCount: 1910\n", "20250527 10:09:0\n", "Date: 20250527 10:09:00 US/Eastern, Open: 355.38, High: 356.3, Low: 355.35, Close: 355.96, Volume: 299677, WAP: 355.828, BarCount: 1484\n", "20250527 10:10:0\n", "Date: 20250527 10:10:00 US/Eastern, Open: 355.92, High: 356.3, Low: 355.56, Close: 356.12, Volume: 256336, WAP: 356.02, BarCount: 1332\n", "20250527 10:11:0\n", "Date: 20250527 10:11:00 US/Eastern, Open: 356.12, High: 356.83, Low: 355.33, Close: 355.54, Volume: 343124, WAP: 356.091, BarCount: 1880\n", "20250527 10:12:0\n", "Date: 20250527 10:12:00 US/Eastern, Open: 355.53, High: 356.12, Low: 354.95, Close: 355.94, Volume: 373159, WAP: 355.477, BarCount: 1798\n", "20250527 10:13:0\n", "Date: 20250527 10:13:00 US/Eastern, Open: 356, High: 356.68, Low: 355.78, Close: 356.49, Volume: 276968, WAP: 356.292, BarCount: 1264\n", "20250527 10:14:0\n", "Date: 20250527 10:14:00 US/Eastern, Open: 356.49, High: 356.87, Low: 355.95, Close: 356.17, Volume: 285303, WAP: 356.424, BarCount: 1351\n", "20250527 10:15:0\n", "Date: 20250527 10:15:00 US/Eastern, Open: 356.19, High: 356.58, Low: 355.75, Close: 355.9, Volume: 205550, WAP: 356.151, BarCount: 1090\n", "20250527 10:16:0\n", "Date: 20250527 10:16:00 US/Eastern, Open: 355.94, High: 356.27, Low: 355.77, Close: 355.9, Volume: 146178, WAP: 355.994, BarCount: 835\n", "20250527 10:17:0\n", "Date: 20250527 10:17:00 US/Eastern, Open: 355.94, High: 356.54, Low: 355.61, Close: 356.38, Volume: 272080, WAP: 356.083, BarCount: 1286\n", "20250527 10:18:0\n", "Date: 20250527 10:18:00 US/Eastern, Open: 356.28, High: 357.39, Low: 356.23, Close: 357.37, Volume: 417929, WAP: 356.875, BarCount: 1870\n", "20250527 10:19:0\n", "Date: 20250527 10:19:00 US/Eastern, Open: 357.35, High: 357.41, Low: 356.9, Close: 357.37, Volume: 305415, WAP: 357.174, BarCount: 1444\n", "20250527 10:20:0\n", "Date: 20250527 10:20:00 US/Eastern, Open: 357.34, High: 357.61, Low: 356.7, Close: 357.1, Volume: 355632, WAP: 357.171, BarCount: 1711\n", "20250527 10:21:0\n", "Date: 20250527 10:21:00 US/Eastern, Open: 357.09, High: 357.2, Low: 356.48, Close: 356.58, Volume: 124879, WAP: 356.78, BarCount: 564\n", "1748356594.5332973\n"]}, {"name": "stdout", "output_type": "stream", "text": ["xxxError:  -1   2108   Market data farm connection is inactive but should be available upon demand.cafarm\n", "xxxError:  -1   2108   Market data farm connection is inactive but should be available upon demand.cafarm\n", "xxxError:  -1   2108   Market data farm connection is inactive but should be available upon demand.cashfarm\n", "xxxError:  -1   2108   Market data farm connection is inactive but should be available upon demand.cashfarm\n"]}], "source": ["\n", "import nest_asyncio\n", "nest_asyncio.apply()\n", "\n", "\n", "from threading import Thread, Event\n", "import pandas as pd\n", "import pandas_ta as ta\n", "import yfinance as yf\n", "\n", "from lightweight_charts import Chart\n", "from ibapi.wrapper import EWrapper\n", "from ibapi.client import EClient\n", "import time \n", "from ibapi.common import *\n", "from ibapi.contract import Contract\n", "from typing import Any\n", "import decimal\n", "\n", "\n", "\n", "class ibapp(EClient, EWrapper):\n", "    def __init__(self):\n", "        EClient.__init__(self, self)\n", "        self.done = Event()  # use threading.Event to signal between threads\n", "        self.connection_ready = Event()  # to signal the connection has been established\n", "        self.bars = [] \n", "\n", "    # override Ewrapper.error\n", "    def error(\n", "        self, reqId: TickerId, errorCode: int, errorString: str, contract: Any = None\n", "    ):\n", "        print(\"xxxError: \", reqId, \" \", errorCode, \" \", errorString)\n", "        if errorCode == 502:  # not connected\n", "            # set self.done (a threading.Event) to True\n", "            self.done.set()\n", "\n", "    def historicalData(self, reqId, bar):\n", "        clean_date = bar.date[:16]\n", "        self.bars.append([clean_date, bar.open, bar.high, bar.low, bar.close, bar.volume])\n", "        print(clean_date)\n", "        print(bar)\n", "\n", "\n", "\n", "app =  ibapp()\n", "\n", "app.connect(\"127.0.0.1\", 7497, clientId=5)  \n", "\n", "time.sleep(1)\n", "\n", "\n", "\n", "api_thread = Thread(target=app.run, args=(), daemon=True)\n", "api_thread.start()\n", "\n", "time.sleep(1)\n", "\n", "con = Contract()\n", "con.symbol = \"TSLA\"\n", "con.secType = \"STK\"\n", "con.exchange = \"SMART\"\n", "con.currency = \"USD\"\n", "con.primaryExchange = \"NASDAQ\"\n", "\n", "app.reqHistoricalData(0, con, \"\", \"1 D\", \"1 min\", \"TRADES\", 0, 1, False, [])   \n", "\n", "print(time.time())\n", "time.sleep(2)\n", "\n", "print(time.time())\n", "df = pd.DataFrame(app.bars, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])\n", "\n", "df = df.map(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)\n", "\n", "  \n", " "]}, {"cell_type": "code", "execution_count": 2, "id": "905e404f", "metadata": {}, "outputs": [], "source": ["def get_bar_data(symbol, timeframe):\n", "    con = Contract()\n", "    con.symbol = symbol\n", "    con.secType = \"STK\"\n", "    con.exchange = \"SMART\"\n", "    con.currency = \"USD\"\n", "    con.primaryExchange = \"NASDAQ\"\n", "    print(app)\n", "    app.reqHistoricalData(0, con, \"\", \"1 D\", '30 mins', \"TRADES\", 0, 1, False, [])   \n", "    \n", "    time.sleep(2)\n", "\n", "    df = pd.DataFrame(app.bars, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])\n", "    df = df.map(lambda x: float(x) if isinstance(x, decimal.Decimal) else x)\n", "    return df\n", "\n", "\n", "def on_search(chart, searched_string):  # Called when the user searches.\n", "    new_data = get_bar_data(searched_string, chart.topbar['timeframe'].value)\n", "    if new_data.empty:\n", "        return\n", "    chart.topbar['symbol'].set(searched_string)\n", "    chart.set(new_data)\n", "\n", "\n", "def on_timeframe_selection(chart):  # Called when the user changes the timeframe.\n", "    new_data = get_bar_data(chart.topbar['symbol'].value, chart.topbar['timeframe'].value)\n", "    if new_data.empty:\n", "        return\n", "    chart.set(new_data, True)\n", "\n", "\n", "def on_horizontal_line_move(chart, line):\n", "    print(f'Horizontal line moved to: {line.price}')\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a602b0be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.ibapp object at 0x000001897EE295E0>\n", "Error:  0   504   Not connected\n"]}, {"data": {"text/plain": ["<lightweight_charts.drawings.HorizontalLine at 0x1891f5b1d30>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "chart = Chart(toolbox=True)\n", "chart.legend(True)\n", "\n", "chart.events.search += on_search\n", "\n", "chart.topbar.textbox('symbol', 'TSLA')\n", "chart.topbar.switcher('timeframe', ('1 min', '5 mins', '30 mins'), default='5 mins',\n", "                        func=on_timeframe_selection)\n", "\n", "df = get_bar_data('TSLA', '5 mins')\n", "chart.set(df)\n", "\n", "chart.horizontal_line(200, func=on_horizontal_line_move)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "e48d2096", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 5\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m      4\u001b[0m loop \u001b[38;5;241m=\u001b[39m asyncio\u001b[38;5;241m.\u001b[39mget_event_loop()\n\u001b[1;32m----> 5\u001b[0m \u001b[43mloop\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_until_complete\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchart\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshow_async\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\nest_asyncio.py:92\u001b[0m, in \u001b[0;36m_patch_loop.<locals>.run_until_complete\u001b[1;34m(self, future)\u001b[0m\n\u001b[0;32m     90\u001b[0m     f\u001b[38;5;241m.\u001b[39m_log_destroy_pending \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m     91\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m f\u001b[38;5;241m.\u001b[39mdone():\n\u001b[1;32m---> 92\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_once\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     93\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_stopping:\n\u001b[0;32m     94\u001b[0m         \u001b[38;5;28;01<PERSON>ak\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\nest_asyncio.py:115\u001b[0m, in \u001b[0;36m_patch_loop.<locals>._run_once\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    108\u001b[0m     heappop(scheduled)\n\u001b[0;32m    110\u001b[0m timeout \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m    111\u001b[0m     \u001b[38;5;241m0\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m ready \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_stopping\n\u001b[0;32m    112\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mmin\u001b[39m(\u001b[38;5;28mmax\u001b[39m(\n\u001b[0;32m    113\u001b[0m         scheduled[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39m_when \u001b[38;5;241m-\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtime(), \u001b[38;5;241m0\u001b[39m), \u001b[38;5;241m86400\u001b[39m) \u001b[38;5;28;01mif\u001b[39;00m scheduled\n\u001b[0;32m    114\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m--> 115\u001b[0m event_list \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_selector\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    116\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_events(event_list)\n\u001b[0;32m    118\u001b[0m end_time \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_clock_resolution\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py:323\u001b[0m, in \u001b[0;36mSelectSelector.select\u001b[1;34m(self, timeout)\u001b[0m\n\u001b[0;32m    321\u001b[0m ready \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m    322\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 323\u001b[0m     r, w, _ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_select\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_readers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_writers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    324\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n\u001b[0;32m    325\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ready\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py:314\u001b[0m, in \u001b[0;36mSelectSelector._select\u001b[1;34m(self, r, w, _, timeout)\u001b[0m\n\u001b[0;32m    313\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_select\u001b[39m(\u001b[38;5;28mself\u001b[39m, r, w, _, timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m--> 314\u001b[0m     r, w, x \u001b[38;5;241m=\u001b[39m \u001b[43mselect\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mw\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mw\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    315\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m r, w \u001b[38;5;241m+\u001b[39m x, []\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}, {"name": "stdout", "output_type": "stream", "text": ["<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n", "<__main__.ibapp object at 0x000001C0FFDC4D40>\n"]}], "source": ["import asyncio\n", "\n", "\n", "loop = asyncio.get_event_loop()\n", "loop.run_until_complete(chart.show_async())\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "52441159", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TSLA\n"]}], "source": ["print(chart.topbar['symbol'].value)\n", "\n", "chart.set(df)\n", "chart.show()"]}, {"cell_type": "code", "execution_count": 8, "id": "60bc40a8", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DataFrame' object has no attribute 'name'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_22024\\4194418571.py\u001b[0m in \u001b[0;36m?\u001b[1;34m()\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mchart\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdf\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\lightweight_charts\\abstract.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, series, _from_tick)\u001b[0m\n\u001b[0;32m    582\u001b[0m         \u001b[0mUpdates\u001b[0m \u001b[0mthe\u001b[0m \u001b[0mdata\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0ma\u001b[0m \u001b[0mbar\u001b[0m\u001b[1;33m;\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    583\u001b[0m         if series['time'] is the same time as the last bar, the last bar will be overwritten.\\n\n\u001b[0;32m    584\u001b[0m         \u001b[1;33m:\u001b[0m\u001b[0mparam\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mlabels\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mdate\u001b[0m\u001b[1;33m/\u001b[0m\u001b[0mtime\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mopen\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mhigh\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mlow\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mclose\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mvolume\u001b[0m \u001b[1;33m(\u001b[0m\u001b[1;32mif\u001b[0m \u001b[0musing\u001b[0m \u001b[0mvolume\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    585\u001b[0m         \"\"\"\n\u001b[1;32m--> 586\u001b[1;33m         \u001b[0mseries\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_series_datetime_format\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseries\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mif\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[0m_from_tick\u001b[0m \u001b[1;32melse\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    587\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'time'\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m!=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_last_bar\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'time'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    588\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcandle_data\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcandle_data\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mindex\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;33m-\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_last_bar\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    589\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcandle_data\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mconcat\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcandle_data\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mto_frame\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mT\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mignore_index\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mTrue\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\lightweight_charts\\abstract.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, series, exclude_lowercase)\u001b[0m\n\u001b[0;32m    206\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_series_datetime_format\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mSeries\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mexclude_lowercase\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    207\u001b[0m         \u001b[0mseries\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcopy\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 208\u001b[1;33m         \u001b[0mseries\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mindex\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_format_labels\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseries\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mindex\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mexclude_lowercase\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    209\u001b[0m         \u001b[0mseries\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'time'\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_single_datetime_format\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mseries\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'time'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    210\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mseries\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\generic.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   6295\u001b[0m             \u001b[1;32mand\u001b[0m \u001b[0mname\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_accessors\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6296\u001b[0m             \u001b[1;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_info_axis\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_can_hold_identifiers_and_holds_name\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6297\u001b[0m         \u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   6298\u001b[0m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 6299\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mobject\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__getattribute__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m: 'DataFrame' object has no attribute 'name'"]}], "source": ["chart.update(df)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}