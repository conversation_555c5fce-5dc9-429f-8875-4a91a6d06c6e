{"cells": [{"cell_type": "code", "execution_count": null, "id": "363efc68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-02 18:53:14 Attempt Connecting...\n", "openOrderEnd\n", "nextValidId. orderId: 12\n", "error. reqId: -1, errorCode: 2104, errorString: Market data farm connection is OK:usfarm.nj, contract: \n", "error. reqId: -1, errorCode: 2104, errorString: Market data farm connection is OK:jfarm, contract: \n", "error. reqId: -1, errorCode: 2104, errorString: Market data farm connection is OK:cashfarm, contract: \n", "error. reqId: -1, errorCode: 2104, errorString: Market data farm connection is OK:cafarm, contract: \n", "error. reqId: -1, errorCode: 2104, errorString: Market data farm connection is OK:usfarm, contract: \n", "error. reqId: -1, errorCode: 2106, errorString: HMDS data farm connection is OK:ushmds, contract: \n", "error. reqId: -1, errorCode: 2158, errorString: Sec-def data farm connection is OK:secdefil, contract: \n", "2025-06-02 18:53:16 Connection attempt 1 : Success\n", "finish\n"]}, {"name": "stdout", "output_type": "stream", "text": ["openOrder. orderId: 0, contract: 265598,AAPL,STK,,,0,?,,SMART,,USD,AAPL,NMS,False,,,,combo:, order: 0,0,751014294: LMT BUY 1@1 DAY, orderState: <ibapi.order_state.OrderState object at 0x0000018AF51D5460>\n", "orderStatus. orderId: 0, status: Submitted, filled: 0, remaining: 1, avgFillPrice: 0.0, permId: 751014294, parentId: 0, lastFillPrice: 0.0, clientId: 0, whyHeld: , mktCapPrice: 0.0\n", "openOrder. orderId: 0, contract: 265598,AAPL,STK,,,0,?,,SMART,,USD,AAPL,NMS,False,,,,combo:, order: 0,0,492222667: LMT BUY 1@150 GTC, orderState: <ibapi.order_state.OrderState object at 0x0000018AF51D6330>\n", "orderStatus. orderId: 0, status: Submitted, filled: 0, remaining: 1, avgFillPrice: 0.0, permId: 492222667, parentId: 0, lastFillPrice: 0.0, clientId: 0, whyHeld: , mktCapPrice: 0.0\n"]}], "source": ["from threading import Thread, Event\n", "from ibapi.wrapper import EWrapper\n", "from ibapi.client import EClient\n", "from ibapi.common import *\n", "from ibapi.contract import Contract\n", "from ibapi.order import Order\n", "from ibapi.order_state import OrderState\n", "\n", "from datetime import datetime\n", "from functools import wraps\n", "\n", "from pandas import DataFrame as df\n", "\n", "from typing import Any\n", "import json \n", "import time\n", "from collections import defaultdict\n", "import inspect\n", "from decimal import Decimal\n", "\n", "\n", "# from rich import print\n", "# from rich.traceback import install\n", "\n", "# install()\n", "\n", "class IBTool(EClient, EWrapper):\n", "    def __init__(self, config):\n", "        EClient.__init__(self, self)\n", "        self.config = config\n", "        self.errorCodes = {502: \"can't connect to TWS\", 504: \"no connection\",326: \"client id is already in use\"}\n", "        self.nextOrderId = 0\n", "        self.reqId = 1\n", "        self.reponses = {}\n", "        self.accountSummaryData = {}\n", "        self.accountData = {}\n", "        self.protfolioData = {}\n", "        self.accountUpdateTime = \"\"\n", "        self.openOrderResponse  = Response()\n", "        self.openOrdersData = []\n", "        self.openStatusData = []\n", "        self.reqResMapping = {}\n", "\n", "\n", "        self.register_reqResMapping(self.reqAccountUpdates, self.accountDownloadEnd)\n", "        self.register_reqResMapping(self.placeOrder, self.openOrderEnd)\n", "\n", "    def nextValidId(self, orderId: int):   \n", "        self.nextOrderId = orderId\n", "        print(f\"nextValidId. orderId: {orderId}\")\n", "\n", "    @staticmethod\n", "    def req_ID_wrapper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", "            Logger.log(f\"Requesting {func.__name__}\")\n", "\n", "            if args :\n", "                reqId = args[0]\n", "            elif 'reqId' in kwargs:\n", "                reqId = kwargs['reqId']\n", "            else:\n", "                raise Exception(\"Request Warpper require reqId as first argument\")\n", "            \n", "            response = Response(reqId)\n", "            self.reponses[reqId] = response\n", "            \n", "            self.reqId += 1\n", "            reult = func(self, *args, **kwargs)\n", "            return response\n", "        return wrapper\n", "\n", "    @staticmethod\n", "    def end_callback_ID_wrapper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", "            \n", "          \n", "            Logger.log(f\"EndCallback {func.__name__}\")\n", "\n", "            result = func(self, *args, **kwargs)\n", "            \n", "            if args :\n", "                reqId = args[0]\n", "            elif 'reqId' in kwargs:\n", "                reqId = kwargs['reqId']\n", "            else:\n", "                raise Exception(\"Callback Warpper require reqId as first argument\")\n", "            \n", "            response = self.reponses[reqId]\n", "            response.done.set()\n", "            self.reponses.pop(reqId)\n", "\n", "            return result\n", "        return wrapper\n", "    \n", "\n", "            \n", "    @staticmethod\n", "    def callback_ID_wrapper(func):\n", "        @wraps(func)\n", "        def wrapper(self, *args, **kwargs):\n", " \n", "            sig = inspect.signature(func)\n", "            bound_args = sig.bind(self, *args, **kwargs)\n", "            bound_args.apply_defaults()\n", "            params = dict(bound_args.arguments)\n", "            params.pop('self', None)\n", "            \n", "            if 'reqId' in params:\n", "                reqId = params['reqId']\n", "            else:\n", "    \n", "                reqId = args[0]\n", "\n", "            record = {\"callback\": func.__name__}\n", "            record.update(params)\n", "            \n", "       \n", "            if reqId not in self.reponses:\n", "                self.reponses[reqId] = Response(reqId)\n", "        \n", "            self.reponses[reqId].contents.append(record)\n", "        \n", "            return func(self, *args, **kwargs)\n", "        return wrapper\n", "    \n", "\n", "    @staticmethod\n", "    def req_warpper(func):\n", "        @wraps(func)    \n", "        def wrapper(self, *args, **kwargs):\n", "            Logger.log(f\"Requesting {func.__name__}\")\n", "            key =  func.__name__\n", "            mapping = self.reqResMapping.get(key)\n", "            if mapping is None:\n", "                raise Exception(f\"Request Warpper require reqResMapping for {key}\")\n", "            \n", "            mapping.done.clear()\n", "            result = func(self, *args, **kwargs)\n", "\n", "            return mapping\n", "        return wrapper\n", "\n", "\n", "    @staticmethod\n", "    def end_callback_warpper(func):\n", "        @wraps(func)    \n", "        def wrapper(self, *args, **kwargs):\n", "            Logger.log(f\"EndCallback {func.__name__}\")\n", "            key =  func.__name__\n", "            mapping = self.reqResMapping.get(key)\n", "            if mapping is None:\n", "                raise Exception(f\"Request Warpper require reqResMapping for {key}\")\n", "            \n", "            mapping.done.set()\n", "            result = func(self, *args, **kwargs)\n", "            return result\n", "        return wrapper\n", "\n", "\n", "    #This is subscribion update , response.contents will not be used\n", "    @end_callback_ID_wrapper\n", "    def accountSummaryEnd(self, reqId: int):\n", "        response = self.reponses[reqId]\n", "        for content in response.contents:\n", "            if content['account'] not in self.accountSummaryData :\n", "                self.accountSummaryData[content['account']] = {}\n", "            \n", "            self.accountSummaryData[content['account']][content['tag']] = {'currency': content['currency'], 'value': content['value'], 'account': content['account'], 'tag': content['tag']}\n", " \n", "           \n", "        print(  self.accountSummaryData)\n", "\n", "    def historicalData(self, reqId: int, bar: BarData):\n", "        self.reponses[reqId].contents.append({\"reqId\": reqId, \"bar\": bar})\n", "        print(f\"historicalData. reqId: {reqId}, bar: {bar}\")\n", "    \n", "    def historicalDataUpdate(self, reqId: int, bar: BarData):\n", "        #self.reponses[reqId].contents.append({\"reqId\": reqId, \"bar\": bar})\n", "        print(f\"historicalDataUpdate. reqId: {reqId}, bar: {bar}\")\n", "\n", "\n", "\n", "    def updatePortfolio(self, contract: Contract, position: float,\n", "                        marketPrice: float, marketValue: float,\n", "                        averageCost: float, unrealizedPNL: float,\n", "                        realizedPNL: float, accountName: str):\n", "\n", "        if accountName not in self.protfolioData:\n", "            self.protfolioData[accountName] = {}\n", "        \n", "        self.protfolioData[accountName][contract.conId] = {'contract': contract, 'position': position, 'marketPrice': marketPrice, 'marketValue': marketValue, 'averageCost': averageCost, 'unrealizedPNL': unrealizedPNL, 'realizedPNL': realizedPNL, 'accountName': accountName}\n", "         \n", "        \n", "        print(f\"updatePortfolio. contract: {contract}, position: {position}, marketPrice: {marketPrice}, marketValue: {marketValue}, averageCost: {averageCost}, unrealizedPNL: {unrealizedPNL}, realizedPNL: {realizedPNL}, accountName: {accountName}\")\n", "\n", "\n", "    def updateAccountValue(self, key: str, val: str, currency: str,\n", "                          accountName: str):\n", "        \n", "        if accountName not in self.accountData:\n", "            self.accountData[accountName] = {}\n", "        \n", "        self.accountData[accountName][key] = {'val': val, 'currency': currency, 'accountName': accountName}\n", "       \n", "\n", "        print(f\"updateAccountValue. key: {key}, val: {val}, currency: {currency}, accountName: {accountName}\")\n", "\n", "    def updateAccountTime(self, timeStamp: str):\n", "        self.accountUpdateTime = timeStamp\n", "        print(f\"updateAccountTime. timeStamp: {timeStamp}\")\n", "\n", "\n", "\n", "    def req<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        self.openOrderResponse.done.clear()\n", "        self.openOrdersData = []\n", "        super().reqAllOpenOrders()\n", "        print(\"reqAllOpenOrders\")\n", "        return self.openOrderResponse\n", "         \n", "    def openOrder(self, orderId: OrderId, contract: Contract, order: Order,\n", "                  orderState: OrderState):\n", "        self.openOrdersData.append({'orderId': orderId, 'contract': contract, 'order': order, 'orderState': orderState})\n", "        print(f\"openOrder. orderId: {orderId}, contract: {contract}, order: {order}, orderState: {orderState}\")\n", "        \n", "    def orderStatus(self, orderId: OrderId, status: str, filled: Decimal,\n", "                    remaining: Decimal, avgFillPrice: float, permId: int,\n", "                    parentId: int, lastFillPrice: float, clientId: int,\n", "                    whyHeld: str, mktCapPrice: float):  \n", "        self.openOrdersData.append({'orderId': orderId, 'status': status, 'filled': filled, 'remaining': remaining, 'avgFillPrice': avgFillPrice, 'permId': permId, 'parentId': parentId, 'lastFillPrice': lastFillPrice, 'clientId': clientId, 'whyHeld': whyHeld, 'mktCapPrice': mktCapPrice})\n", "        print(f\"orderStatus. orderId: {orderId}, status: {status}, filled: {filled}, remaining: {remaining}, avgFillPrice: {avgFillPrice}, permId: {permId}, parentId: {parentId}, lastFillPrice: {lastFillPrice}, clientId: {clientId}, whyHeld: {whyHeld}, mktCapPrice: {mktCapPrice}\")   \n", "\n", "\n", "\n", "    def openOrderEnd(self):\n", "        self.openOrderResponse.done.set()\n", "        print(\"openOrderEnd\")\n", "        \n", "    \n", "    def connect(self, host, port, clientId):\n", "        \n", "        self.reponses[0] = Response(0)\n", "            \n", "        return super().connect(host, port, clientId)\n", "\n", "\n", "\n", "\n", "    def error(self, reqId: TickerId, errorCode: int, errorString: str, contract: Any = None):\n", "        print(f\"error. reqId: {reqId}, errorCode: {errorCode}, errorString: {errorString}, contract: {contract}\")\n", "\n", "        # if reqId == -1:\n", "        #     reqId = 0\n", "\n", "        # response = self.reponses[reqId]\n", "        # msg =  {\"reqId\": reqId, \"errorCode\": errorCode, \"errorString\": errorString}\n", "        # if str.find(errorString,\"error\") != -1:\n", "        #     response.errors.append(msg)\n", "        # elif str.find(errorString,\"warning\") != -1:\n", "        #     response.warnings.append(msg)\n", "        # else:\n", "        #     response.infos.append(msg)\n", "        \n", "     \n", "\n", "\n", "\n", "    def register_reqResMapping(self,req_func, resp_func):\n", "        response = Response()\n", "        response.req_func = req_func\n", "        response.resp_func = resp_func\n", "       \n", "        self.reqResMapping[req_func.__name__] = response\n", "        self.reqResMapping[resp_func.__name__] = response\n", "\n", " \n", "\n", "IBTool.reqHistoricalData = IBTool.req_ID_wrapper(IBTool.reqHistoricalData)\n", "IBTool.historicalDataEnd = IBTool.end_callback_ID_wrapper(IBTool.historicalDataEnd)\n", "\n", "IBTool.reqAccountSummary = IBTool.req_ID_wrapper(IBTool.reqAccountSummary)\n", "IBTool.accountSummary = IBTool.callback_ID_wrapper(IBTool.accountSummary)\n", "\n", "IBTool.reqAccountUpdates = IBTool.req_warpper(IBTool.reqAccountUpdates)\n", "IBTool.accountDownloadEnd = IBTool.end_callback_warpper(IBTool.accountDownloadEnd)\n", "\n", "\n", "\n", "\n", "class Response():\n", "\n", "    def __init__(self, reqId = 0):\n", "        self.reqId = reqId\n", "        self.done = Event()\n", "        self.contents = []\n", "        self.errors = []\n", "        self.warnings = []\n", "        self.infos = []\n", "        self.req_func = None\n", "        self.resp_func  = None\n", "\n", "\n", "\n", "class Logger():\n", "    def __init__(self, config):\n", "        self.config = config\n", "\n", "    def log(self, message):\n", "        print(message)\n", "\n", "    def log(message):\n", "        print(f\"{datetime.now():%Y-%m-%d %H:%M:%S} {message}\")\n", "\n", "class TradeTool():\n", "    def __init__(self):\n", "        self.config = 'config.json'\n", "        self.ib_Thread = None\n", "        self.ib = None\n", "\n", "        with open(self.config) as f:\n", "            self.config = json.load(f)\n", "\n", "        self.logger = Logger(self.config)\n", "       \n", "    \n", "    def start(self):\n", "        self.connect()\n", "        con = Contract()\n", "        con.symbol = \"TSLA\"\n", "        con.secType = \"STK\"\n", "        con.exchange = \"SMART\"\n", "        con.currency = \"USD\"\n", "        con.primaryExchange = \"NASDAQ\"\n", "\n", "        # response = self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])\n", "        # response.done.wait()\n", "    \n", "        # print(df( response.contents))\n", "        \n", "        #response = self.ib.reqAccountSummary(reqId=self.ib.reqId, groupName=\"All\", tags=\"TotalCashValue,NetLiquidation\")\n", "        #response.done.wait()\n", "        #aa = self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=False, chartOptions=[])\n", "        \n", "        #bb = self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"3000 S\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n", "        \n", "        #self.ib.reqAllOpenOrders().wait()\n", "\n", "\n", "        #response =self.ib.reqHistoricalData(reqId=self.ib.reqId, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n", "        \n", "        \n", "        # oox=Order()\n", "        # oox.action = \"BUY\"\n", "        # oox.orderType = \"LMT\"\n", "        # oox.totalQuantity = 1\n", "        # oox.lmtPrice = 10\n", "        # response = self.ib.placeOrder(orderId=self.ib.nextOrderId, contract=con, order=oox)\n", "        # response.done.wait()\n", "        # response = self.ib.reqAllOpenOrders()\n", " \n", "\n", "\n", "        #time.sleep(10)\n", "   \n", "        print(\"finish\")\n", "\n", "\n", "    def connect (self):\n", "        \n", "        attempt = 0\n", "        isConnect = False\n", "        while isConnect == False and attempt < 10:\n", "            Logger.log(\"Attempt Connecting...\")\n", "\n", "            if self.ib != None:\n", "                self.ib.disconnect()\n", "                self.ib = None\n", "                time.sleep(1)\n", "\n", "            self.ib = IBTool(self.config)\n", "            self.ib.connect(self.config['host'], self.config['port'], clientId=self.config['clientId'])\n", "            self.ib_Thread = Thread(target=self.ib.run,args=(), daemon=True)\n", "            self.ib_Thread.start()\n", "            attempt += 1\n", "            time.sleep(2)\n", "\n", "            isConnect = self.ib.isConnected()\n", "            Logger.log(f\"Connection attempt {attempt} : {[\"Not Success\",\"Success\"][isConnect]}\")   \n", "            \n", "\n", "            \n", "tt = TradeTool()\n", "tt.start()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9f22baad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-05-31 21:19:34 Requesting reqHistoricalData\n", "2025-05-31 21:19:35 Requesting reqHistoricalData\n"]}], "source": ["con = Contract()\n", "con.symbol = \"TSLA\"\n", "con.secType = \"STK\"\n", "con.exchange = \"SMART\"\n", "con.currency = \"USD\"\n", "con.primaryExchange = \"NASDAQ\"\n", "\n", "r =tt.ib.reqHistoricalData( reqId=7, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n", "r.done.wait()\n", "\n", "con.symbol = \"TEM\"\n", "\n", "r =tt.ib.reqHistoricalData( reqId=7, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n", "r.done.wait()\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "db01366e", "metadata": {}, "outputs": [], "source": ["tt.ib.nextOrderId = 11\n", "\n", "con = Contract()\n", "con.symbol = \"NVDA\"\n", "con.secType = \"STK\"\n", "con.exchange = \"SMART\"\n", "con.currency = \"USD\"\n", "con.primaryExchange = \"NASDAQ\"\n", "\n", "oox=Order()\n", "oox.action = \"BUY\"\n", "oox.orderType = \"LMT\"\n", "oox.tif = \"DAY\"\n", "oox.totalQuantity = 1\n", "oox.lmtPrice = 5\n", "\n", "tt.ib.placeOrder(orderId=tt.ib.nextOrderId, contract=con, order=oox)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2662dd84", "metadata": {}, "outputs": [], "source": ["from ibapi.order_cancel import OrderCancel\n", "\n", "# 創建一個 OrderCancel 物件\n", "orderCancelObj = OrderCancel()\n", "orderCancelObj.manualOrderCancelTime = \"\"  # 如果不需要特殊的取消時間，可設置為空字符串，或填入其他適當的值\n", "\n", "# 正確調用 cancelOrder 方法\n", "tt.ib.cancelOrder(orderId=11, orderCancel=orderCancelObj)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c998e8e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["reqAllOpenOrders\n"]}, {"data": {"text/plain": ["<__main__.Response at 0x18a94ac78c0>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["tt.ib.reqAllOpenOrders()\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6efc2278", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-01 03:23:15 Requesting reqHistoricalData\n"]}, {"data": {"text/plain": ["<__main__.Response at 0x2420620f290>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["con = Contract()\n", "con.symbol = \"NVDA\"\n", "con.secType = \"STK\"\n", "con.exchange = \"SMART\"\n", "con.currency = \"USD\"\n", "con.primaryExchange = \"NASDAQ\"\n", "\n", "\n", "tt.ib.reqHistoricalData( reqId=7, contract=con, endDateTime=\"\", durationStr=\"1 D\", barSizeSetting=\"1 min\", whatToShow=\"TRADES\", useRTH=0, formatDate=1, keepUpToDate=True, chartOptions=[])\n"]}, {"cell_type": "code", "execution_count": 6, "id": "fc5cb730", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-02 11:31:30 Requesting reqAccountUpdates\n"]}, {"data": {"text/plain": ["<__main__.Response at 0x18a94cc7d70>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tt.ib.reqAccountUpdates(subscribe=False, acctCode=\"\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c1eca2db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["嘗試連線...\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:usfarm.nj\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:cafarm\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:cashfarm\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:usfarm\n", "錯誤回調: reqId=-1, errorCode=2106, errorString=HMDS data farm connection is OK:euhmds\n", "錯誤回調: reqId=-1, errorCode=2106, errorString=HMDS data farm connection is OK:fundfarm\n", "錯誤回調: reqId=-1, errorCode=2106, errorString=HMDS data farm connection is OK:ushmds\n", "錯誤回調: reqId=-1, errorCode=2158, errorString=Sec-def data farm connection is OK:secdefil\n", "執行歷史資料請求...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:jfarm\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:jfarm\n", "錯誤回調: reqId=-1, errorCode=2103, errorString=Market data farm connection is broken:jfarm\n", "錯誤回調: reqId=-1, errorCode=2104, errorString=Market data farm connection is OK:jfarm\n"]}], "source": ["import time\n", "from functools import wraps\n", "\n", "def require_connection(func):\n", "    @wraps(func)\n", "    def wrapper(self, *args, **kwargs):\n", "        if not self.isConnected():\n", "            print(f\"[{func.__name__}] 未連線，等待連線中...\")\n", "            # 這裡可以選擇反覆等待，或是直接返回錯誤訊息\n", "            timeout = 10  # 最多等待 10 秒\n", "            waited = 0\n", "            while not self.isConnected() and waited < timeout:\n", "                time.sleep(1)\n", "                waited += 1\n", "            if not self.isConnected():\n", "                print(f\"[{func.__name__}] 連線狀態仍不正常，請先連線！\")\n", "                return\n", "        return func(self, *args, **kwargs)\n", "    return wrapper\n", "\n", "\n", "from ibapi.wrapper import EWrapper\n", "from ibapi.client import EClient\n", "from ibapi.contract import Contract\n", "\n", "class MyIBClient(EClient, EWrapper):\n", "    def __init__(self):\n", "        EClient.__init__(self, self)\n", "    \n", "    # EWrapper.error作為回調\n", "    def error(self, reqId, errorCode, errorString, contract=None):\n", "        print(f\"錯誤回調: reqId={reqId}, errorCode={errorCode}, errorString={errorString}\")\n", "\n", "    # 假設我們要封裝 reqHistoricalData 的呼叫\n", "    @require_connection\n", "    def safe_reqHistoricalData(self, reqId, contract, endDateTime,\n", "                               durationStr, barSizeSetting, whatToShow,\n", "                               useRTH, formatDate, keepUpToDate, chartOptions):\n", "        print(\"執行歷史資料請求...\")\n", "        self.reqHistoricalData(reqId, contract, endDateTime,\n", "                               durationStr, barSizeSetting, whatToShow,\n", "                               useRTH, formatDate, keepUpToDate, chartOptions)\n", "\n", "# 測試用例\n", "if __name__ == \"__main__\":\n", "    import nest_asyncio\n", "    nest_asyncio.apply()\n", "    \n", "    import time\n", "    from threading import Thread\n", "\n", "    app = MyIBClient()\n", "    host = \"127.0.0.1\"\n", "    port = 7497\n", "    clientId = 5\n", "\n", "    print(\"嘗試連線...\")\n", "    app.connect(host, port, clientId)\n", "    \n", "    # 用獨立執行緒啟動 API 的事件迴圈\n", "    api_thread = Thread(target=app.run, daemon=True)\n", "    api_thread.start()\n", "    \n", "    # 模擬等待連線建立：如果連線建立失敗，則 safe_reqHistoricalData 替你等待或報錯\n", "    time.sleep(2)\n", "    \n", "    con = Contract()\n", "    con.symbol = \"TSLA\"\n", "    con.secType = \"STK\"\n", "    con.exchange = \"SMART\"\n", "    con.currency = \"USD\"\n", "    con.primaryExchange = \"NASDAQ\"\n", "    \n", "    # 呼叫包裝過的安全 request 方法，在發送請求前會先確認連線狀態\n", "    app.safe_reqHistoricalData(\n", "        reqId=0,\n", "        contract=con,\n", "        endDateTime=\"\",\n", "        durationStr=\"1 D\",\n", "        barSizeSetting=\"1 min\",\n", "        whatToShow=\"TRADES\",\n", "        useRTH=0,\n", "        formatDate=1,\n", "        keepUpToDate=False,\n", "        chartOptions=[]\n", "    )\n", "    \n", "    # 保持主程序不退出，等待回調\n", "    time.sleep(5)\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}