#!/usr/bin/env python3
"""
測試內存追蹤功能
"""

from ibapi.contract import Contract
import sys

def test_contract_details_tracking():
    """
    測試合約詳情請求的內存追蹤
    """
    print("=== 開始內存追蹤測試 ===\n")
    
    # 創建合約
    con = Contract()
    con.symbol = "TEM" 
    con.secType = "STK"
    con.exchange = "NASDAQ"
    con.currency = "USD"
    con.primaryExchange = "NASDAQ"
    
    print(f"合約信息:")
    print(f"  Symbol: {con.symbol}")
    print(f"  Exchange: {con.exchange}")
    print(f"  Currency: {con.currency}")
    
    print(f"\n=== 創建請求 ===")
    
    # 創建請求 - 這會自動使用 TrackedList
    req2 = tt.ib.reqContractDetails(6, con)
    
    print(f"請求已創建:")
    print(f"  Request ID: {req2.reqId}")
    print(f"  Request type: {req2.type}")
    print(f"  Contents 對象地址: {hex(id(req2.response.contents))}")
    print(f"  Contents 初始長度: {len(req2.response.contents)}")
    print(f"  Contents 類型: {type(req2.response.contents)}")
    
    print(f"\n=== 開始 delay_wait() ===")
    print("現在會監控所有對 contents 的修改...")
    
    # 執行請求 - 所有對 contents 的修改都會被追蹤
    req2.done.delay_wait()
    
    print(f"\n=== delay_wait() 完成 ===")
    
    print(f"最終結果:")
    print(f"  Contents 長度: {len(req2.response.contents)}")
    print(f"  Contents 值: {req2.response.contents}")
    print(f"  請求錯誤: {req2.response.errors}")
    print(f"  請求警告: {req2.response.warnings}")
    print(f"  請求是否完成: {req2.done.is_set()}")
    print(f"  請求是否在列表中: {req2 in tt.ib.requests}")
    
    # 如果 contents 是 TrackedList，顯示操作歷史
    if hasattr(req2.response.contents, 'operation_log'):
        print(f"\n=== 操作歷史 ===")
        print(f"總共記錄了 {len(req2.response.contents.operation_log)} 個操作:")
        
        for i, log in enumerate(req2.response.contents.operation_log):
            print(f"\n操作 {i+1}: {log['operation']}")
            print(f"  參數: {log['args']}")
            print(f"  長度變化: {log['list_length_before']} -> {len(req2.response.contents)}")
            print(f"  調用者: {log['caller_info']}")
    
    return req2

def analyze_results(req2):
    """
    分析結果
    """
    print(f"\n=== 結果分析 ===")
    
    if len(req2.response.contents) == 0:
        print("❌ 問題確認: contents 是空的!")
        
        if hasattr(req2.response.contents, 'operation_log'):
            # 查找 clear 操作
            clear_operations = [log for log in req2.response.contents.operation_log if log['operation'] == 'clear']
            
            if clear_operations:
                print(f"🔍 發現 {len(clear_operations)} 個 clear 操作:")
                for i, clear_op in enumerate(clear_operations):
                    print(f"\nClear 操作 {i+1}:")
                    print(f"  調用者: {clear_op['caller_info']}")
                    print(f"  調用堆棧:")
                    for frame in clear_op['stack_trace']:
                        print(f"    {frame.filename}:{frame.lineno} in {frame.name}()")
            else:
                print("🤔 沒有發現 clear 操作，可能是其他原因導致的空列表")
        
        # 檢查錯誤
        if req2.response.errors:
            print(f"\n📋 請求錯誤:")
            for error in req2.response.errors:
                print(f"  {error}")
    
    else:
        print(f"✅ 成功: contents 有 {len(req2.response.contents)} 個項目")
        if len(req2.response.contents) > 0:
            print(f"第一個項目的合約ID: {req2.response.contents[0].contract.conId}")

if __name__ == "__main__":
    print("內存追蹤測試腳本")
    print("使用方法:")
    print("1. req2 = test_contract_details_tracking()")
    print("2. analyze_results(req2)")
