"""
Quick and simple callback print capture for immediate use in Jupyter notebooks
Copy and paste these functions directly into your notebook cells
"""

import sys
import io
import time
import threading
from contextlib import contextmanager


# === SIMPLE SOLUTIONS FOR IMMEDIATE USE ===

def capture_prints_for_seconds(seconds=10):
    """
    Capture all print output for the specified number of seconds
    Use this right before executing code that has callback prints
    """
    print(f"🔍 Capturing prints for {seconds} seconds...")
    
    captured_output = []
    original_stdout = sys.stdout
    
    class PrintCapture:
        def write(self, text):
            original_stdout.write(text)  # Still display in notebook
            if text.strip():  # Only capture non-empty text
                timestamp = time.strftime('%H:%M:%S')
                captured_output.append(f"[{timestamp}] {text}")
        
        def flush(self):
            original_stdout.flush()
    
    # Replace stdout
    sys.stdout = PrintCapture()
    
    # Set timer to restore stdout
    def restore_stdout():
        time.sleep(seconds)
        sys.stdout = original_stdout
        
        # Show summary
        if captured_output:
            print(f"\n📋 Captured {len(captured_output)} callback prints:")
            print("=" * 50)
            for line in captured_output:
                print(line, end='')
            print("=" * 50)
        else:
            print(f"\n✅ No callback prints captured in {seconds} seconds")
    
    # Start timer in background thread
    timer = threading.Thread(target=restore_stdout, daemon=True)
    timer.start()
    
    return captured_output


@contextmanager
def capture_callback_output():
    """
    Context manager to capture callback prints
    
    Usage:
    with capture_callback_output():
        # Your code that triggers callbacks
        req.done.delay_wait()
    """
    old_stdout = sys.stdout
    captured = io.StringIO()
    
    class DualOutput:
        def write(self, text):
            old_stdout.write(text)  # Show in notebook
            captured.write(text)    # Capture for later
        
        def flush(self):
            old_stdout.flush()
            captured.flush()
    
    sys.stdout = DualOutput()
    
    try:
        yield captured
    finally:
        sys.stdout = old_stdout
        output = captured.getvalue()
        if output:
            print("\n📝 Captured callback output:")
            print("-" * 40)
            print(output, end='')
            print("-" * 40)


def wrap_function_with_print_capture(func, func_name=None):
    """
    Wrap any function to capture its print output
    
    Usage:
    wrapped_delay_wait = wrap_function_with_print_capture(req.done.delay_wait, "delay_wait")
    wrapped_delay_wait()
    """
    func_name = func_name or getattr(func, '__name__', 'function')
    
    def wrapper(*args, **kwargs):
        print(f"🔄 Executing {func_name}...")
        
        old_stdout = sys.stdout
        captured = io.StringIO()
        sys.stdout = captured
        
        try:
            result = func(*args, **kwargs)
            output = captured.getvalue()
            
            # Restore stdout
            sys.stdout = old_stdout
            
            # Show captured output
            if output:
                print(f"📝 Output from {func_name}:")
                print(output, end='')
            else:
                print(f"✅ {func_name} completed (no output)")
            
            return result
            
        except Exception as e:
            sys.stdout = old_stdout
            print(f"❌ Error in {func_name}: {e}")
            raise
    
    return wrapper


# === SPECIFIC SOLUTIONS FOR YOUR TRADING CODE ===

def monitor_trading_request(request_obj, request_name="Request"):
    """
    Monitor a trading request object and capture all callback prints
    
    Usage:
    req = tt.ib.reqContractDetails(9, contract)
    monitor_trading_request(req, "ContractDetails")
    """
    print(f"🔍 Monitoring {request_name}...")
    
    if hasattr(request_obj, 'done') and hasattr(request_obj.done, 'delay_wait'):
        # Wrap the delay_wait method
        original_delay_wait = request_obj.done.delay_wait
        request_obj.done.delay_wait = wrap_function_with_print_capture(
            original_delay_wait, f"{request_name}.delay_wait"
        )
        
        print(f"✅ {request_name} monitoring setup complete")
        print("   Now call req.done.delay_wait() to see captured output")
    else:
        print(f"⚠️ {request_name} doesn't have delay_wait method to monitor")
    
    return request_obj


def quick_contract_details_test(tt_instance, symbol="AAPL"):
    """
    Quick test function with full callback capture
    
    Usage:
    quick_contract_details_test(tt, "AAPL")
    """
    from ibapi.contract import Contract
    
    print(f"🧪 Testing contract details for {symbol} with callback capture...")
    
    # Create contract
    contract = Contract()
    contract.symbol = symbol
    contract.secType = "STK"
    contract.exchange = "SMART"
    contract.currency = "USD"
    
    # Start print capture
    captured_prints = capture_prints_for_seconds(15)  # Capture for 15 seconds
    
    # Make request
    req = tt_instance.ib.reqContractDetails(9, contract)
    print(f"📤 Request sent for {symbol}")
    
    # Monitor the request
    req = monitor_trading_request(req, f"ContractDetails-{symbol}")
    
    # Wait for completion
    req.done.delay_wait()
    
    # Show results
    print(f"\n📊 Final Results for {symbol}:")
    print(f"   Contents: {len(req.response.contents)} items")
    print(f"   Errors: {len(req.response.errors)} errors")
    
    return req, captured_prints


# === COPY-PASTE READY CODE FOR NOTEBOOK ===

def setup_callback_capture():
    """
    One-line setup for callback capture
    Copy this into your notebook cell and run it
    """
    print("🔧 Callback capture utilities loaded!")
    print("\nQuick usage:")
    print("1. capture_prints_for_seconds(10)  # Capture for 10 seconds")
    print("2. monitor_trading_request(req)    # Monitor specific request")
    print("3. quick_contract_details_test(tt) # Full test with capture")
    print("\nAdvanced usage:")
    print("4. with capture_callback_output(): req.done.delay_wait()")
    print("5. wrapped_func = wrap_function_with_print_capture(func)")


# Example of how to use in your notebook:
NOTEBOOK_EXAMPLE = '''
# === COPY THIS INTO YOUR NOTEBOOK CELL ===

# 1. Load the capture utilities
exec(open('quick_callback_capture.py').read())
setup_callback_capture()

# 2. Method 1: Simple capture for next 10 seconds
capture_prints_for_seconds(10)
# Now run your code that has callbacks...

# 3. Method 2: Monitor specific request
req = tt.ib.reqContractDetails(9, contract)
req = monitor_trading_request(req, "ContractDetails")
req.done.delay_wait()

# 4. Method 3: Context manager
with capture_callback_output():
    req.done.delay_wait()

# 5. Method 4: Quick test
quick_contract_details_test(tt, "AAPL")
'''

if __name__ == "__main__":
    print("Quick Callback Capture Utilities")
    print("=" * 40)
    print(NOTEBOOK_EXAMPLE)
