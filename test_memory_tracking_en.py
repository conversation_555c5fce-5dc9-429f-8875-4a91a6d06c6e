#!/usr/bin/env python3
"""
Test memory tracking functionality - English only version
"""

from ibapi.contract import Contract
import sys

def test_contract_details_tracking():
    """
    Test contract details request with memory tracking
    """
    print("=== Starting Memory Tracking Test ===\n")
    
    # Create contract
    con = Contract()
    con.symbol = "AAPL"  # Use AAPL for better reliability
    con.secType = "STK"
    con.exchange = "SMART"
    con.currency = "USD"
    
    print(f"Contract info:")
    print(f"  Symbol: {con.symbol}")
    print(f"  Exchange: {con.exchange}")
    print(f"  Currency: {con.currency}")
    
    print(f"\n=== Creating Request ===")
    
    # Create request - this will automatically use TrackedList
    req2 = tt.ib.reqContractDetails(8, con)
    
    print(f"Request created:")
    print(f"  Request ID: {req2.reqId}")
    print(f"  Request type: {req2.type}")
    print(f"  Contents object address: {hex(id(req2.response.contents))}")
    print(f"  Contents initial length: {len(req2.response.contents)}")
    print(f"  Contents type: {type(req2.response.contents)}")
    
    print(f"\n=== Starting delay_wait() ===")
    print("Now monitoring all modifications to contents...")
    
    # Execute request - all modifications to contents will be tracked
    req2.done.delay_wait()
    
    print(f"\n=== delay_wait() completed ===")
    
    print(f"Final results:")
    print(f"  Contents length: {len(req2.response.contents)}")
    print(f"  Contents value: {req2.response.contents}")
    print(f"  Request errors: {req2.response.errors}")
    print(f"  Request warnings: {req2.response.warnings}")
    print(f"  Request completed: {req2.done.is_set()}")
    print(f"  Request in list: {req2 in tt.ib.requests}")
    
    # If contents is TrackedList, show operation history
    if hasattr(req2.response.contents, 'operation_log'):
        print(f"\n=== Operation History ===")
        print(f"Total recorded operations: {len(req2.response.contents.operation_log)}")
        
        for i, log in enumerate(req2.response.contents.operation_log):
            print(f"\nOperation {i+1}: {log['operation']}")
            print(f"  Args: {log['args']}")
            print(f"  Length change: {log['list_length_before']} -> {len(req2.response.contents)}")
            print(f"  Caller: {log['caller_info']}")
    
    return req2

def analyze_results(req2):
    """
    Analyze results
    """
    print(f"\n=== Result Analysis ===")
    
    if len(req2.response.contents) == 0:
        print("ERROR CONFIRMED: contents is empty!")
        
        if hasattr(req2.response.contents, 'operation_log'):
            # Look for clear operations
            clear_operations = [log for log in req2.response.contents.operation_log if log['operation'] == 'clear']
            
            if clear_operations:
                print(f"FOUND {len(clear_operations)} clear operations:")
                for i, clear_op in enumerate(clear_operations):
                    print(f"\nClear operation {i+1}:")
                    print(f"  Caller: {clear_op['caller_info']}")
                    print(f"  Call stack:")
                    for frame in clear_op['stack_trace']:
                        print(f"    {frame.filename}:{frame.lineno} in {frame.name}()")
            else:
                print("No clear operations found, might be other cause for empty list")
        
        # Check errors
        if req2.response.errors:
            print(f"\nRequest errors:")
            for error in req2.response.errors:
                print(f"  {error}")
    
    else:
        print(f"SUCCESS: contents has {len(req2.response.contents)} items")
        if len(req2.response.contents) > 0:
            print(f"First item contract ID: {req2.response.contents[0].contract.conId}")

def simple_test():
    """
    Simple test without tt object dependency
    """
    print("=== Simple TrackedList Test ===")
    
    # Test TrackedList directly
    from Comm import Response
    test_response = Response()
    
    print(f"Response.contents type: {type(test_response.contents)}")
    
    if hasattr(test_response.contents, 'operation_log'):
        print("TrackedList is working!")
        test_response.contents.append("test item")
        print(f"Operation log length: {len(test_response.contents.operation_log)}")
        
        for i, log in enumerate(test_response.contents.operation_log):
            print(f"  Operation {i+1}: {log['operation']} - {log['caller_info']}")
    else:
        print("TrackedList is NOT working")

if __name__ == "__main__":
    print("Memory tracking test script loaded!")
    print("Available functions:")
    print("1. simple_test() - Test TrackedList without tt object")
    print("2. test_contract_details_tracking() - Full test with contract details")
    print("3. analyze_results(req2) - Analyze results after test")
