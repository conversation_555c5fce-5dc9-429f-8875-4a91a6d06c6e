from threading import Thread, Event, current_thread
import time
from datetime import datetime
from enum import IntEnum

import inspect

import traceback
import sys

class TrackedList(list):
    """
    強化版追蹤列表 - 類似 Cheat Engine 的內存寫入監控
    """

    def __init__(self, name="TrackedList"):
        super().__init__()
        self.name = name
        self.operation_log = []

        # 獲取創建者信息
        stack = traceback.extract_stack()
        creator_info = self._get_creator_info(stack)

        print(f"🔍 TrackedList '{name}' created at address {hex(id(self))}")
        print(f"   Created by: {creator_info['class_method']}")
        print(f"   Location: {creator_info['file']}:{creator_info['line']}")
        print(f"   Code: {creator_info['code']}")

        # 顯示完整的7層堆棧
        print(f"   📍 Creation Stack (7 levels):")
        self._print_creation_stack(stack, levels=7)

        # 保存創建者信息
        self.creator_info = creator_info

    def _log_operation(self, operation: str, *args):
        """記錄操作和調用堆棧"""
        stack = traceback.extract_stack()
        # 過濾掉這個方法本身的堆棧
        filtered_stack = [frame for frame in stack if 'TrackedList' not in frame.filename]

        log_entry = {
            'operation': operation,
            'args': str(args)[:100],
            'list_length_before': len(self),
            'stack_trace': filtered_stack[-5:],  # 只保留最近5層調用
            'caller_info': self._get_caller_info(filtered_stack)
        }

        self.operation_log.append(log_entry)

        # 即時輸出重要操作
        if operation in ['clear', 'pop', '__delitem__', '__setitem__']:
            print(f"🚨 {self.name} WRITE DETECTED: {operation}")
            print(f"   Caller: {log_entry['caller_info']}")
            print(f"   Length: {log_entry['list_length_before']} -> {len(self)}")
            self._print_stack_trace()

    def _get_caller_info(self, stack):
        """獲取調用者信息"""
        if len(stack) >= 2:
            caller = stack[-2]
            return f"{caller.filename}:{caller.lineno} in {caller.name}()"
        return "Unknown caller"

    def _get_creator_info(self, stack):
        """獲取創建者詳細信息"""
        # 過濾掉 TrackedList 自己的堆棧和追蹤相關的代碼
        filtered_stack = []
        for frame in stack:
            # 跳過追蹤相關的代碼
            if ('TrackedList' in frame.filename or
                'traceback.extract_stack' in str(frame.line) or
                'stack = traceback.extract_stack' in str(frame.line) or
                '_print_creation_stack' in frame.name or
                '_get_creator_info' in frame.name or
                frame.name == '__init__' and 'TrackedList' in str(frame.line)):
                continue
            filtered_stack.append(frame)

        if len(filtered_stack) >= 1:
            creator = filtered_stack[-1]

            # 嘗試確定類和方法
            class_method = self._determine_class_method(creator)

            return {
                'class_method': class_method,
                'file': creator.filename.split('\\')[-1],  # 只顯示文件名
                'line': creator.lineno,
                'code': creator.line.strip() if creator.line else 'N/A',
                'full_path': creator.filename
            }

        return {
            'class_method': 'Unknown',
            'file': 'Unknown',
            'line': 0,
            'code': 'N/A',
            'full_path': 'Unknown'
        }

    def _determine_class_method(self, frame):
        """嘗試確定類和方法名"""
        function_name = frame.name
        filename = frame.filename
        code_line = frame.line.strip() if frame.line else ""

        # 如果是 __init__ 方法，嘗試找到類名
        if function_name == '__init__':
            if 'Response()' in code_line:
                return 'Response.__init__()'
            elif 'Request(' in code_line:
                return 'Request.__init__()'
            elif 'TrackedList(' in code_line:
                return 'TrackedList.__init__()'

        # 根據文件名和函數名推斷
        if 'Comm.py' in filename:
            if function_name == '__init__':
                return f'Comm.{function_name}()'
            else:
                return f'Comm.{function_name}()'
        elif 'IBTool.py' in filename:
            # 嘗試從代碼行推斷更具體的信息
            if 'register_requests' in function_name:
                return f'IBTool.register_requests()'
            elif 'reqContractDetails' in function_name:
                return f'IBTool.reqContractDetails()'
            elif 'contractDetails' in function_name:
                return f'IBTool.contractDetails()'
            else:
                return f'IBTool.{function_name}()'
        elif 'TradeTool.py' in filename:
            return f'TradeTool.{function_name}()'
        elif '.ipynb' in filename or 'ipython' in filename.lower():
            return f'Notebook.{function_name}()'
        else:
            file_short = filename.split("/")[-1].split("\\")[-1]
            return f'{file_short}.{function_name}()'

    def _print_stack_trace(self):
        """打印詳細的調用堆棧"""
        print("📍 STACK TRACE:")
        stack = traceback.extract_stack()
        for i, frame in enumerate(stack[-8:-1]):  # 顯示最近7層調用
            print(f"   {i+1}. {frame.filename}:{frame.lineno} in {frame.name}()")
            if frame.line:
                print(f"      Code: {frame.line.strip()}")

    def _print_creation_stack(self, stack, levels=7):
        """打印創建時的堆棧信息"""
        # 過濾掉 TrackedList 自己的堆棧和追蹤相關的代碼
        filtered_stack = []
        for frame in stack:
            # 跳過 TrackedList 內部的追蹤代碼
            if ('TrackedList' in frame.filename or
                'traceback.extract_stack' in str(frame.line) or
                'stack = traceback.extract_stack' in str(frame.line) or
                '_print_creation_stack' in frame.name or
                '_get_creator_info' in frame.name):
                continue
            filtered_stack.append(frame)

        # 從最近的調用開始，顯示指定層數
        start_index = max(0, len(filtered_stack) - levels)
        relevant_stack = filtered_stack[start_index:]

        for i, frame in enumerate(relevant_stack):
            level = i + 1
            filename = frame.filename.split('\\')[-1]  # 只顯示文件名

            # 嘗試確定類和方法
            class_method = self._determine_class_method(frame)

            print(f"     {level}. {filename}:{frame.lineno} in {class_method}")
            if frame.line:
                print(f"        Code: {frame.line.strip()}")

            # 添加更詳細的註釋
            annotation = self._get_frame_annotation(frame)
            if annotation:
                print(f"        💡 {annotation}")

    def _get_frame_annotation(self, frame):
        """獲取框架的註釋說明"""
        function_name = frame.name
        code_line = frame.line.strip() if frame.line else ""
        filename = frame.filename

        # 根據函數名和代碼內容提供註釋
        if 'reqContractDetails' in function_name:
            return "Main contract details request function"
        elif 'register_requests' in function_name:
            return "Registers request and creates Response object"
        elif function_name == '__init__' and 'Response' in code_line:
            return "Response object initialization"
        elif function_name == '__init__' and 'TrackedList' in code_line:
            return "TrackedList object creation"
        elif 'wrapper' in function_name:
            return "Request wrapper/decorator function"
        elif '<module>' in function_name:
            if '.ipynb' in filename or 'ipython' in filename.lower():
                return "Jupyter notebook cell execution"
            else:
                return "Module-level execution"
        elif 'start' in function_name and 'TradeTool' in filename:
            return "TradeTool startup/initialization"
        elif 'connect' in function_name.lower():
            return "Connection establishment"
        elif 'delay_wait' in code_line:
            return "Waiting for request completion"
        elif 'exec(' in code_line:
            return "Dynamic code execution"
        elif 'test_' in function_name:
            return "Test function execution"
        elif 'run' in function_name and 'cell' in code_line.lower():
            return "Notebook cell runner"

        return None

    def append(self, item):
        self._log_operation('append', item)
        result = super().append(item)
        print(f"✅ {self.name}.append({str(item)[:50]}) - Length now: {len(self)}")
        return result

    def clear(self):
        self._log_operation('clear')
        result = super().clear()
        print(f"🚨 {self.name}.clear() - Length now: {len(self)}")
        print(f"   ⚠️  CONTENTS CLEARED! This might be your issue!")
        return result

    def pop(self, index=-1):
        self._log_operation('pop', index)
        result = super().pop(index)
        print(f"🗑️ {self.name}.pop({index}) = {str(result)[:50]} - Length now: {len(self)}")
        return result

    def remove(self, item):
        self._log_operation('remove', item)
        result = super().remove(item)
        print(f"🗑️ {self.name}.remove({str(item)[:50]}) - Length now: {len(self)}")
        return result

    def __setitem__(self, key, value):
        self._log_operation('__setitem__', key, value)
        result = super().__setitem__(key, value)
        print(f"✏️ {self.name}[{key}] = {str(value)[:50]}")
        return result

    def __delitem__(self, key):
        self._log_operation('__delitem__', key)
        result = super().__delitem__(key)
        print(f"🗑️ del {self.name}[{key}] - Length now: {len(self)}")
        return result

    def show_object_info(self):
        """顯示對象的詳細信息"""
        print(f"\n📋 TrackedList Object Info:")
        print(f"   Name: {self.name}")
        print(f"   Address: {hex(id(self))}")
        print(f"   Current Length: {len(self)}")
        print(f"   Created by: {self.creator_info['class_method']}")
        print(f"   Created at: {self.creator_info['file']}:{self.creator_info['line']}")
        print(f"   Creation code: {self.creator_info['code']}")
        print(f"   Total operations: {len(self.operation_log)}")

        if self.operation_log:
            print(f"   Recent operations:")
            for log in self.operation_log[-3:]:  # 顯示最近3個操作
                print(f"     - {log['operation']} at {log['caller_info']}")

    def find_who_owns_me(self):
        """找出誰擁有這個對象"""
        import gc

        print(f"\n🔍 Finding who owns TrackedList at {hex(id(self))}:")

        # 獲取所有引用這個對象的對象
        referrers = gc.get_referrers(self)

        for i, ref in enumerate(referrers):
            ref_type = type(ref).__name__
            ref_id = hex(id(ref))

            print(f"   Referrer {i+1}: {ref_type} at {ref_id}")

            # 如果是字典，嘗試找到鍵
            if isinstance(ref, dict):
                for key, value in ref.items():
                    if value is self:
                        print(f"     -> Found as dict['{key}']")

                        # 如果這個字典是某個對象的 __dict__
                        dict_owners = gc.get_referrers(ref)
                        for owner in dict_owners:
                            if hasattr(owner, '__dict__') and owner.__dict__ is ref:
                                print(f"     -> Dict belongs to {type(owner).__name__} at {hex(id(owner))}")

                                # 如果是 Response 對象，顯示更多信息
                                if hasattr(owner, 'errors') and hasattr(owner, 'warnings'):
                                    print(f"        -> This is a Response object!")

                                    # 找到擁有這個 Response 的 Request
                                    response_owners = gc.get_referrers(owner)
                                    for resp_owner in response_owners:
                                        if hasattr(resp_owner, 'response') and resp_owner.response is owner:
                                            print(f"        -> Response belongs to Request {type(resp_owner).__name__}")
                                            if hasattr(resp_owner, 'reqId'):
                                                print(f"           Request ID: {resp_owner.reqId}")
                                            if hasattr(resp_owner, 'type'):
                                                print(f"           Request Type: {resp_owner.type}")

            # 如果是列表，顯示位置
            elif isinstance(ref, list):
                try:
                    index = ref.index(self)
                    print(f"     -> Found at list[{index}]")
                except ValueError:
                    pass

class DebugList(list):
    def clear(self):
        print(">>> DebugList.clear() called")
        for line in traceback.format_stack()[-6:-1]:
            print(line.strip())
        return super().clear()
    def pop(self, i=-1):
        print(f">>> DebugList.pop({i}) called")
        for line in traceback.format_stack()[-6:-1]:
            print(line.strip())
        return super().pop(i)
    def remove(self, v):
        print(f">>> DebugList.remove({v!r}) called")
        for line in traceback.format_stack()[-6:-1]:
            print(line.strip())
        return super().remove(v)
    def __delitem__(self, idx):
        print(f">>> DebugList.__delitem__({idx}) called")
        for line in traceback.format_stack()[-6:-1]:
            print(line.strip())
        return super().__delitem__(idx)



class WARNING_CANCEL_UPDATEACCOUNT:
    MESSAGES = {
        2100: "从 TWS请 求 了 新 账 户 数 据 。 API客 户 已 从 账 户 数 据 中 取 消 订阅 。"
    }


class WARNING:
    MESSAGES = {
        399,  "Order Message: BUY 1 TSLA NASDAQ.NMS Warning: Your order will not be placed at the exchange until xx time.",
        10167, "Requested market data is not subscribed. Displaying delayed market data. "
    }    

class INFO_TWS_AVILABLE:
    FAIL_MESSAGES = {
        1100: "Connectivity between IBKR and Trader Workstation has been lost., contract",
    }

    SUCCESS_MESSAGES = {
        1102:"Connectivity between IBKR and Trader Workstation has been restored - data maintained. All data farms are connected: usfarm.nj; jfarm; cafarm; cashfarm; usfarm; euhmds; fundfarm; ushmds; secdefil., contract"
    }

class ERROR_CONNECTION():
    MESSAGES = {
        502:"不 能 连 接 TWS。 通 过 Configure>API( 配 置 >API) 菜 单 指 令 确认 API已 在 TWS中 启 用",
        503:"你 的 TWS版 本 已 过 期 , 必 须 更 新",
        504:"没 有 连 接 。",
        1100: "Connectivity between IBKR and Trader Workstation has been lost., contract"
    }


class CLIENT_REQUEST_ERRORS:
    CONNECTION_UNAVAILABLE = {"errorCode":99910,"errorString":"Connection unavailable"}
    TIME_OUT = {"errorCode":99911,"errorString":"Request timed out"}


class MESSAGE_DONE:
    MESSAGES = {
        202: "Order Canceled - reason",
        162: "Historical Market Data Service error message:API historical data query cancelled"
    }

class MESSAGE_INPUT_ERROR:
    MESSAGES = {
        321: "Input Incorrect"
    }

class TickerID_RULES:
    SYSTEM = -1
    REQUEST_ID_MIN = 0
    REQUEST_ID_MAX = 20
    


class REQUEST_TYPES(IntEnum):
    PLACE_ORDER = 1
    UPDATE_ORDER = 2
    CANCEL_ORDER = 3
    GET_OPEN_ORDERS = 4
    GET_EXECUTIONS = 5
    GET_COMPLETED_ORDERS = 6
    GET_POSITIONS = 7
    CANCEL_POSITIONS = 8
    GET_IDS = 9
    UPDATE_ACCOUNT_SUMMARY = 10
    CANCEL_ACCOUNT_SUMMARY = 11
    UPDATE_ACCOUNT = 12
    CANCEL_UPDATE_ACCOUNT = 13
    GET_HISTORICAL_DATA = 14
    CANCEL_HISTORICAL_DATA = 15
    GET_REAL_TIME_BARS = 16       
    CANCEL_REAL_TIME_BARS = 17     
    GET_MARKET_DATA = 18
    CANCEL_MARKET_DATA = 19
    GET_HISTORICAL_TICKS = 20
    CANCEL_HISTORICAL_TICKS = 21
    GET_TICK_BY_TICK_DATA = 22
    CANCEL_TICK_BY_TICK_DATA = 23
    GET_FUNDAMENTAL_DATA = 24
    CANCEL_FUNDAMENTAL_DATA = 25
    GET_MARKET_DEPTH = 26
    CANCEL_MARKET_DEPTH = 27
    GET_MARKET_DEPTH_L2 = 28
    CANCEL_MARKET_DEPTH_L2 = 29
    GET_CURRENT_TIME = 30
    GET_MARKET_DATA_TYPE = 31
    CONNECT = 32
    GET_CONTRACT_DETAILS = 33
    GET_WSH_EVENT_DATA = 34
    CANCEL_WSH_EVENT_DATA = 35
    GET_WSH_META_DATA = 36
    CANCEL_WSH_META_DATA = 37
    REQUEST_ERROR = 99999

class REQUEST_TYPES_ALLOW_DUPLICATE:
    LIST = [REQUEST_TYPES.GET_HISTORICAL_DATA]
   


class DelayEvent(Event):
    def __init__(self, timeout: float = 0.0,delay: float = 0.0, callbackWhenFinished = None):
        super().__init__()
        self.delay = delay
        self.timeout = timeout
        self.callbackWhenFinished = callbackWhenFinished


    def delay_wait(self):
        result = False

        if self.timeout > 0.0:
            result = super().wait(self.timeout)
          
        else:
            result = super().wait()
    
        
        time.sleep(self.delay)
        if self.callbackWhenFinished is not None:
            self.callbackWhenFinished(self, result)

        return result



class Request():
    def __init__(self, type : REQUEST_TYPES, reqId: int = -1, keepAlive: bool = False, timeout: float = 0.0, delayWhenFinished: float = 0.0, callbackWhenFinished = None, noResponse: bool = False):

        self.type = type
        self.reqId = reqId
        self.done = DelayEvent(timeout, delayWhenFinished , callbackWhenFinished = self.handle_when_done)
        self.keepAlive = keepAlive
        self.response = Response()
        self.callbackWhenFinished = callbackWhenFinished
        self.isTimeout = False
        self.notRequireResponse =  noResponse
   

    def handle_when_done(self, event: DelayEvent, timeout: bool):

        self.isTimeout = timeout
        if timeout == True:
            if  self.notRequireResponse == False:
                self.response.errors.append(CLIENT_REQUEST_ERRORS.TIME_OUT)
            

        if self.callbackWhenFinished is not None:
            self.callbackWhenFinished(self)


class Response():

    def __init__(self):
        self.contents = TrackedList("response.contents")
        self.errors = []
        self.warnings = []
        self.infos = []


class Logger():
    def __init__(self, config):
        self.config = config

    def _log(self, message):
        print(message)

    @staticmethod
    def log(message):
        stack = inspect.stack()
        if stack[1].function != "historicalData1":
            print(f"caller:{stack[1].function} {datetime.now():%Y-%m-%d %H:%M:%S} {message}")