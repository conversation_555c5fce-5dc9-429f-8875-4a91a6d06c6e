"""
Utilities for capturing print output from callback functions in Jupyter notebooks
"""

import sys
import io
from contextlib import contextmanager, redirect_stdout, redirect_stderr
from IPython.display import display, HTML
import threading
import time
from typing import Callable, Any, Optional, List


class PrintCapture:
    """Capture print output from functions and callbacks"""
    
    def __init__(self):
        self.captured_output = []
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.capture_buffer = io.StringIO()
        self.is_capturing = False
        
    def start_capture(self):
        """Start capturing print output"""
        self.is_capturing = True
        self.capture_buffer = io.StringIO()
        sys.stdout = self.capture_buffer
        sys.stderr = self.capture_buffer
        
    def stop_capture(self):
        """Stop capturing and return captured output"""
        if self.is_capturing:
            sys.stdout = self.original_stdout
            sys.stderr = self.original_stderr
            output = self.capture_buffer.getvalue()
            self.is_capturing = False
            return output
        return ""
    
    def get_captured_output(self):
        """Get current captured output without stopping capture"""
        if self.is_capturing:
            return self.capture_buffer.getvalue()
        return ""
    
    @contextmanager
    def capture_context(self):
        """Context manager for capturing print output"""
        self.start_capture()
        try:
            yield self
        finally:
            output = self.stop_capture()
            if output:
                print(output, end='')  # Print to original stdout


class CallbackPrintCapture:
    """Enhanced print capture specifically for callback functions"""
    
    def __init__(self, display_immediately=True, prefix="[CALLBACK] "):
        self.display_immediately = display_immediately
        self.prefix = prefix
        self.captured_outputs = []
        self.lock = threading.Lock()
        
    def capture_callback_prints(self, callback_func: Callable, *args, **kwargs):
        """Capture prints from a callback function"""
        capture = PrintCapture()
        
        def wrapped_callback(*cb_args, **cb_kwargs):
            with capture.capture_context():
                result = callback_func(*cb_args, **cb_kwargs)
                output = capture.get_captured_output()
                
                if output:
                    with self.lock:
                        self.captured_outputs.append({
                            'timestamp': time.time(),
                            'output': output,
                            'function': callback_func.__name__
                        })
                        
                        if self.display_immediately:
                            print(f"{self.prefix}{callback_func.__name__}:")
                            print(output, end='')
                            
                return result
                
        return wrapped_callback
    
    def get_all_captured_output(self):
        """Get all captured output from callbacks"""
        with self.lock:
            return self.captured_outputs.copy()
    
    def display_captured_output(self):
        """Display all captured output in notebook"""
        outputs = self.get_all_captured_output()
        if not outputs:
            print("No callback output captured")
            return
            
        for output_info in outputs:
            timestamp = time.strftime('%H:%M:%S', time.localtime(output_info['timestamp']))
            print(f"\n=== {output_info['function']} at {timestamp} ===")
            print(output_info['output'], end='')


@contextmanager
def notebook_print_capture():
    """Simple context manager for capturing prints in notebook cells"""
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    
    stdout_capture = io.StringIO()
    stderr_capture = io.StringIO()
    
    try:
        sys.stdout = stdout_capture
        sys.stderr = stderr_capture
        yield stdout_capture, stderr_capture
    finally:
        sys.stdout = old_stdout
        sys.stderr = old_stderr
        
        # Display captured output
        stdout_content = stdout_capture.getvalue()
        stderr_content = stderr_capture.getvalue()
        
        if stdout_content:
            print("STDOUT:")
            print(stdout_content, end='')
        if stderr_content:
            print("STDERR:")
            print(stderr_content, end='')


def wrap_callback_with_capture(callback_func: Callable, capture_name: str = None) -> Callable:
    """Decorator to wrap callback functions with print capture"""
    capture_name = capture_name or callback_func.__name__
    
    def wrapper(*args, **kwargs):
        print(f"\n🔄 Executing callback: {capture_name}")
        
        # Capture output
        old_stdout = sys.stdout
        captured_output = io.StringIO()
        sys.stdout = captured_output
        
        try:
            result = callback_func(*args, **kwargs)
            output = captured_output.getvalue()
            
            # Restore stdout and display captured output
            sys.stdout = old_stdout
            
            if output:
                print(f"📝 Output from {capture_name}:")
                print(output, end='')
            else:
                print(f"✅ {capture_name} completed (no output)")
                
            return result
            
        except Exception as e:
            sys.stdout = old_stdout
            print(f"❌ Error in {capture_name}: {e}")
            raise
            
    return wrapper


# Example usage functions for your specific use case
def setup_callback_capture_for_trading():
    """Setup print capture specifically for trading callbacks"""
    capture = CallbackPrintCapture(display_immediately=True, prefix="[TRADING] ")
    
    # Example of how to wrap your existing callbacks
    def wrap_trading_callback(original_callback):
        return capture.capture_callback_prints(original_callback)
    
    return capture, wrap_trading_callback


def monitor_delayed_prints(duration_seconds: int = 10):
    """Monitor and capture prints that happen after cell execution"""
    print(f"🔍 Monitoring prints for {duration_seconds} seconds...")
    
    captured_lines = []
    original_stdout = sys.stdout
    
    class MonitoringStdout:
        def write(self, text):
            if text.strip():  # Only capture non-empty lines
                captured_lines.append(f"[{time.strftime('%H:%M:%S')}] {text}")
            original_stdout.write(text)
            
        def flush(self):
            original_stdout.flush()
    
    sys.stdout = MonitoringStdout()
    
    def stop_monitoring():
        time.sleep(duration_seconds)
        sys.stdout = original_stdout
        if captured_lines:
            print(f"\n📋 Captured {len(captured_lines)} delayed prints:")
            for line in captured_lines:
                print(line, end='')
        else:
            print(f"\n✅ No delayed prints captured in {duration_seconds} seconds")
    
    # Start monitoring in background thread
    monitor_thread = threading.Thread(target=stop_monitoring, daemon=True)
    monitor_thread.start()
    
    return captured_lines
