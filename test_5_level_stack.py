#!/usr/bin/env python3
"""
Test 5-level stack tracking
"""

def test_5_level_stack():
    """Test the enhanced 5-level stack tracking"""
    
    print("=== Testing 7-Level Stack Tracking ===\n")
    
    # First, reload Comm to get the enhanced TrackedList
    import importlib
    import Comm
    importlib.reload(Comm)
    
    print("1. Testing basic Response creation:")
    from Comm import Response
    test_response = Response()
    
    print("\n" + "="*60)
    
    # Test with contract details if tt exists
    try:
        if 'tt' in globals():
            print("\n2. Testing with contract details request:")
            
            from ibapi.contract import Contract
            con = Contract()
            con.symbol = "AAPL"
            con.secType = "STK"
            con.exchange = "SMART"
            con.currency = "USD"
            
            # This will show the full 5-level stack
            req2 = tt.ib.reqContractDetails(11, con)
            
            print(f"\nRequest created. Now executing delay_wait()...")
            req2.done.delay_wait()
            
            print(f"\nResults:")
            print(f"  Contents length: {len(req2.response.contents)}")
            print(f"  Errors: {req2.response.errors}")
            
            # Show detailed object info
            req2.response.contents.show_object_info()
            
            return req2
        else:
            print("\ntt object not available")
            return test_response
            
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_stack_comparison():
    """Show comparison between different stack levels"""
    
    print("\n=== Stack Level Comparison ===")
    
    # Create multiple TrackedLists to see different creation paths
    from Comm import Response, TrackedList
    
    print("\n1. Direct TrackedList creation:")
    direct_list = TrackedList("direct_creation")
    
    print("\n2. Through Response creation:")
    response_obj = Response()
    
    print("\n3. Through manual assignment:")
    manual_list = TrackedList("manual_assignment")
    response_obj.contents = manual_list

if __name__ == "__main__":
    print("7-level stack tracking test loaded!")
    print("Use: test_5_level_stack() to run the test")
    print("Use: show_stack_comparison() to compare different creation paths")
