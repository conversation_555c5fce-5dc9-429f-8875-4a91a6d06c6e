%load_ext autoreload
%autoreload 2

import importlib
import Comm
import IBTool
importlib.reload(Comm)
importlib.reload(IBTool)

from rich import print
from rich.traceback import install

install()

import json 



class TradeTool():
    def __init__(self):
        self.config = 'config.json'
        self.ib = None

        with open(self.config) as f:
            self.config = json.load(f)

        self.logger = Comm.Logger(self.config)
       
    
    def start(self):
        
        self.ib =  IBTool.IBTool(self.config)
        self.ib.reconnect()
   
        print("finish")


tt = TradeTool()
tt.start()



%load_ext autoreload
%autoreload 2

import importlib
import Comm
import IBTool
importlib.reload(Comm)
importlib.reload(IBTool)

from rich import print
from rich.traceback import install

install()

import json 



class TradeTool():
    def __init__(self):
        self.config = 'config.json'
        self.ib = None

        with open(self.config) as f:
            self.config = json.load(f)

        self.logger = Comm.Logger(self.config)
       
    
    def start(self):
        
        self.ib =  IBTool.IBTool(self.config)
        self.ib.reconnect()
   
        print("finish")


tt = TradeTool()
tt.start()



# 簡單測試 - 直接在 notebook 中運行
print("=== Testing TrackedList ===")

# Check Response class
from Comm import Response
test_response = Response()
print(f"Response.contents type: {type(test_response.contents)}")

# Test TrackedList functionality
if hasattr(test_response.contents, 'operation_log'):
    print("✅ TrackedList is working!")
    test_response.contents.append("test item")
    print(f"Operation log: {len(test_response.contents.operation_log)}")
else:
    print("❌ TrackedList is NOT working")

# If tt exists, test contract details
try:
    if 'tt' in globals():
        print(f"\nTradeTool status: connected={tt.ib.TWS_connection}, available={tt.ib.TWS_aviliable}")
        
        # Create simple contract request
        from ibapi.contract import Contract
        con = Contract()
        con.symbol = "AAPL"  # Use AAPL for better reliability
        con.secType = "STK"
        con.exchange = "SMART"
        con.currency = "USD"
        
        print(f"\nTesting contract: {con.symbol}")
        req2 = tt.ib.reqContractDetails(9, con)
        print(f"Contents type: {type(req2.response.contents)}")
        
        req2.done.delay_wait()
        
        print(f"Result: length={len(req2.response.contents)}, errors={len(req2.response.errors)}")
        
        if hasattr(req2.response.contents, 'operation_log'):
            print(f"Operation history: {len(req2.response.contents.operation_log)} operations")
            for i, log in enumerate(req2.response.contents.operation_log):
                print(f"  {i+1}. {log['operation']} - {log['caller_info']}")
        
        # Check if contents was cleared
        if len(req2.response.contents) == 0 and hasattr(req2.response.contents, 'operation_log'):
            clear_ops = [log for log in req2.response.contents.operation_log if log['operation'] == 'clear']
            if clear_ops:
                print(f"\n🚨 FOUND THE PROBLEM! Contents was cleared {len(clear_ops)} times:")
                for clear_op in clear_ops:
                    print(f"   Cleared by: {clear_op['caller_info']}")
    else:
        print("\n❌ tt object does not exist")
except Exception as e:
    print(f"\n❌ Error: {e}")


from ibapi.contract import Contract
from ibapi.order import Order
from ibapi.common import WshEventData



con = Contract()
# con.symbol = "TEM" 
con.symbol = "TEM" 
con.secType = "STK"
# con.exchange = "BATS"
con.exchange = "NASDAQ"
con.currency = "USD"
con.primaryExchange = "NASDAQ"


order = Order()
order.action = "BUY"
order.orderType = "LMT"
order.totalQuantity = 1 

req2 = tt.ib.reqContractDetails(1, con) 
req2.done.delay_wait()




startDateTime = "20250601 09:30:00 US/Eastern"
endDateTime   = "20250601 16:00:00 US/Eastern"

wshEventData = WshEventData()



wshEventData.fillWatchlist = True  # Example: request to fill watchlist
wshEventData.fillPortfolio = False  # Example: do not fill portfolio
wshEventData.fillCompetitors = False  # Example: do not fill competitors
print("最终 contents：", req2.response.contents)
wshEventData.conId = req2.response.contents[0].contract.conId 

# Then use:
req = tt.ib.reqWshEventData(5, wshEventData, 1200)
req.done.delay_wait()
print(req.response.warnings)



print(tt.ib.requests)
